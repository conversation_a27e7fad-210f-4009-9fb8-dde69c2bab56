# 🚀 How to Run MELK Chemo Copilot

## 📁 Available Launcher Files

### 1. **run_melk_copilot.bat** (Recommended)
**Full setup and launch script**
- ✅ Automatically creates virtual environment if needed
- ✅ Installs all required dependencies
- ✅ Checks for the correct application file
- ✅ Provides detailed status messages
- ✅ Handles errors gracefully

**Usage:** Double-click `run_melk_copilot.bat` or run from command prompt

### 2. **quick_start.bat**
**Quick launcher for experienced users**
- ⚡ Fast startup (assumes setup is complete)
- ⚠️ Requires virtual environment and dependencies to be already installed
- 🎯 Minimal output, direct launch

**Usage:** Double-click `quick_start.bat` (only after initial setup)

## 🔧 First Time Setup

### Option A: Automatic Setup (Recommended)
1. Double-click `run_melk_copilot.bat`
2. Wait for automatic setup to complete
3. Application will open in your browser

### Option B: Manual Setup
1. Open command prompt in the project folder
2. Create virtual environment: `python -m venv .venv`
3. Activate virtual environment: `.venv\Scripts\activate`
4. Install dependencies: `pip install -r requirements.txt`
5. Run application: `python -m streamlit run app_new.py`

## 📋 System Requirements

- **Python 3.8+** installed and accessible from command line
- **Internet connection** for downloading dependencies
- **Modern web browser** (Chrome, Firefox, Edge, Safari)
- **Windows OS** (for .bat files)

## 🌐 Accessing the Application

Once started, the application will be available at:
- **Local URL:** http://localhost:8520
- **Network URL:** http://************:8520 (accessible from other devices on same network)

## 📊 Test Data

Sample Excel and CSV files are included for testing:
- `X_train.xlsx` / `X_train.csv` - Training spectral data (25 samples × 50 variables)
- `Y_train.xlsx` / `Y_train.csv` - Training concentration data (25 samples × 2 targets)
- `X_test.xlsx` / `X_test.csv` - Test spectral data (8 samples × 50 variables)
- `Y_test.xlsx` / `Y_test.csv` - Test concentration data (8 samples × 2 targets)

## ❌ Removed Files

The following outdated files have been removed:
- ~~`run.bat`~~ - Outdated, used old app.py
- ~~`run_simple.bat`~~ - Outdated, used simple_app.py
- ~~`run.sh`~~ - Linux version, outdated

## 🐛 Troubleshooting

### Application won't start
1. Ensure Python is installed: `python --version`
2. Check virtual environment: `.venv` folder should exist
3. Try manual setup (Option B above)
4. Check requirements.txt exists and is readable

### Dependencies installation fails
1. Update pip: `python -m pip install --upgrade pip`
2. Clear pip cache: `pip cache purge`
3. Install manually: `pip install streamlit pandas numpy scikit-learn openpyxl`

### Browser doesn't open automatically
1. Manually navigate to http://localhost:8520
2. Check if port 8520 is available
3. Try different port: `streamlit run app_new.py --server.port 8521`

### Excel files won't upload
1. Ensure openpyxl is installed: `pip install openpyxl xlrd`
2. Try CSV format instead
3. Check file format matches requirements (samples as rows, variables as columns)

## 📞 Support

If you encounter issues:
1. Check this troubleshooting guide
2. Verify all requirements are met
3. Try the manual setup process
4. Check the console output for specific error messages

## 🎯 Quick Start Checklist

- [ ] Python 3.8+ installed
- [ ] Downloaded/cloned project files
- [ ] Double-clicked `run_melk_copilot.bat`
- [ ] Waited for setup to complete
- [ ] Browser opened to http://localhost:8520
- [ ] Application loaded successfully

**Ready to analyze your spectroscopic data!** 🧪
