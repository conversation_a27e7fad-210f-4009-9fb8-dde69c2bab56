"""
SpectraSpeak - Main Application Entry Point

An intelligent spectroscopic analysis platform built with Streamlit.
"""

import streamlit as st
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import configuration and core modules
# Note: Configuration imports are handled by individual components
from core.workflow_manager import workflow_manager
from core.session_manager import session_manager
from ui.styles import get_complete_css
from ui.logo_component import HeaderComponent, BrandingManager

# Import step classes
from steps.step1_data_upload import DataUploadStep
from steps.step2_data_overview import DataOverviewStep
from steps.step3_preprocessing import PreprocessingStep
from steps.step4_variable_selection import VariableSelectionStep
from steps.step5_model_selection import Step5ModelSelection


def configure_page():
    """Configure the Streamlit page settings with new modular styling."""
    # Use the new branding manager for page configuration
    page_config = BrandingManager.get_page_config()
    st.set_page_config(**page_config)

    # Apply the new modular CSS styling
    st.markdown(get_complete_css(), unsafe_allow_html=True)


def register_workflow_steps():
    """Register all workflow steps with the workflow manager."""
    from steps.step6_model_prediction import Step6ModelPrediction
    from steps.step7_model_prediction import Step7ModelPrediction

    workflow_manager.register_step(1, DataUploadStep)
    workflow_manager.register_step(2, DataOverviewStep)
    workflow_manager.register_step(3, PreprocessingStep)
    workflow_manager.register_step(4, VariableSelectionStep)
    workflow_manager.register_step(5, Step5ModelSelection)
    workflow_manager.register_step(6, Step6ModelPrediction)
    workflow_manager.register_step(7, Step7ModelPrediction)


def render_workflow_integrity_check():
    """Check and display workflow integrity issues."""
    issues = workflow_manager.validate_workflow_integrity()
    if issues:
        st.sidebar.markdown("###   Workflow Issues")
        for issue in issues:
            st.sidebar.warning(issue)


def render_debug_info():
    """Render debug information in sidebar (only in development)."""
    if st.sidebar.checkbox("Show Debug Info", key="debug_mode"):
        st.sidebar.markdown("###  Debug Information")

        # Session state summary
        summary = session_manager.get_data_summary()
        st.sidebar.json(summary)

        # Current session state keys
        with st.sidebar.expander("Session State Keys"):
            for key in st.session_state.keys():
                if not key.startswith('_'):  # Hide internal keys
                    st.sidebar.text(f"{key}: {type(st.session_state[key])}")


def main():
    """Main application function."""
    # Configure the page
    configure_page()

    # Register workflow steps
    register_workflow_steps()

    # Render main header using new component
    HeaderComponent.render_main_header()

    # Create main layout
    # Sidebar for navigation and controls
    with st.sidebar:
        st.markdown("# NAVIGATION")
        workflow_manager.render_sidebar()

        # Add workflow integrity check
        render_workflow_integrity_check()

        # Add debug info in development
        render_debug_info()

    # Main content area
    try:
        # Execute the current workflow step
        current_step = workflow_manager.get_current_step()

        if current_step in [1, 2, 3, 4, 5, 6, 7]:
            # All steps are implemented
            workflow_manager.execute_current_step()
        else:
            # Invalid step
            st.error(f"Invalid step number: {current_step}")
            st.info("Please navigate to a valid step using the sidebar.")

            # Navigation back to previous step
            if st.button(" Back to Previous Step"):
                session_manager.set_current_step(current_step - 1)
                st.rerun()

    except Exception as e:
        st.error(f"An error occurred: {str(e)}")

        # Show error details in debug mode
        if st.checkbox("Show Error Details"):
            st.exception(e)

        # Provide recovery options
        st.markdown("### Recovery Options")
        col1, col2 = st.columns(2)

        with col1:
            if st.button("Reset Current Step"):
                current_step = workflow_manager.get_current_step()
                session_manager.clear_step_data(current_step)
                st.rerun()

        with col2:
            if st.button("Reset Entire Workflow"):
                session_manager.reset_workflow()
                st.rerun()


if __name__ == "__main__":
    main()
