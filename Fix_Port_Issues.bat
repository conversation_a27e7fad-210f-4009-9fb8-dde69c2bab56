@echo off
title SpectraSpeak Port Fix Utility
color 0E

echo.
echo ================================================================
echo                SpectraSpeak Port Fix Utility                
echo ================================================================
echo.
echo This utility will help resolve port conflicts for SpectraSpeak
echo.

REM Check which ports are in use
echo Checking port usage...
echo.

REM Check port 8527
netstat -an | findstr ":8527" >nul
if errorlevel 1 (
    echo [OK] Port 8527 is available
) else (
    echo [WARNING] Port 8527 is in use
)

REM Check port 8528
netstat -an | findstr ":8528" >nul
if errorlevel 1 (
    echo [OK] Port 8528 is available
) else (
    echo [WARNING] Port 8528 is in use
)

REM Check port 8529
netstat -an | findstr ":8529" >nul
if errorlevel 1 (
    echo [OK] Port 8529 is available
) else (
    echo [WARNING] Port 8529 is in use
)

echo.
echo ================================================================
echo                        Solutions                             
echo ================================================================
echo.

echo Option 1: Stop all Python/Streamlit processes
echo [1] Press 1 to stop all Python processes
echo.
echo Option 2: Use alternative port
echo [2] Press 2 to start SpectraSpeak on a random available port
echo.
echo Option 3: Manual instructions
echo [3] Press 3 for manual troubleshooting steps
echo.
echo [Q] Press Q to quit
echo.

set /p choice="Enter your choice (1/2/3/Q): "

if /i "%choice%"=="1" goto stop_processes
if /i "%choice%"=="2" goto random_port
if /i "%choice%"=="3" goto manual_steps
if /i "%choice%"=="q" goto quit
goto invalid_choice

:stop_processes
echo.
echo Stopping all Python processes...
taskkill /F /IM python.exe >nul 2>&1
taskkill /F /IM streamlit.exe >nul 2>&1
echo [OK] Processes stopped. You can now start SpectraSpeak normally.
goto end

:random_port
echo.
echo Starting SpectraSpeak on a random available port...
REM Generate random port between 8530-8599
set /a port=8530 + %random% %% 70
echo Trying port %port%...
python -m streamlit run main.py --server.port %port%
goto end

:manual_steps
echo.
echo Manual Troubleshooting Steps:
echo =============================
echo.
echo 1. Open Task Manager (Ctrl+Alt+Delete)
echo 2. Go to "Processes" tab
echo 3. Look for "python.exe" entries
echo 4. Right-click and select "End Task" for each python.exe
echo 5. Close Task Manager
echo 6. Try starting SpectraSpeak again
echo.
echo Alternative:
echo - Restart your computer to clear all processes
echo - Use a different port by editing the batch files
echo.
goto end

:invalid_choice
echo.
echo [ERROR] Invalid choice. Please try again.
echo.
pause
goto start

:quit
echo.
echo Exiting...
goto end

:end
echo.
pause
