# Sample Data for SpectraSpeak

This folder contains sample datasets for testing and demonstration purposes.

## Data Files

### Trial 1
- Spectroscopic data with training and testing sets
- Includes both CSV and Excel formats
- Suitable for UV-Vis-NIR analysis

### Trial 2
- Additional spectroscopic datasets
- Design files and MATLAB scripts for reference

## Usage

1. Use these files to test SpectraSpeak functionality
2. Upload the training data (X_train, Y_train) in Step 1
3. Upload the test data (X_test, Y_test) for validation
4. Follow the workflow through all steps

## Data Format

- **X_train/X_test**: Spectral data (samples × wavelengths)
- **Y_train/Y_test**: Reference values (samples × analytes)
- Files use standard CSV format with appropriate delimiters
