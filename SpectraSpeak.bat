@echo off
title SpectraSpeak
color 0A

echo.
echo ================================================================
echo                        SpectraSpeak
echo              Intelligent Spectroscopic Analysis
echo ================================================================
echo.

REM Check Python
echo Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found!
    echo Please install Python from https://python.org
    pause
    exit /b 1
)
echo Python OK

REM Setup virtual environment
echo Setting up environment...
if not exist ".venv" (
    python -m venv .venv
)

REM Activate environment
if exist ".venv\Scripts\activate.bat" (
    call .venv\Scripts\activate.bat
    echo Environment activated
)

REM Check Streamlit
python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo Installing packages...
    pip install -r requirements.txt
)

REM Check main file
if not exist "main.py" (
    echo ERROR: main.py not found!
    pause
    exit /b 1
)

echo.
echo Starting SpectraSpeak...
echo Browser will open automatically
echo Keep this window open!
echo.

REM Start application
python -m streamlit run main.py --server.port 8527

echo.
echo SpectraSpeak closed.
pause
