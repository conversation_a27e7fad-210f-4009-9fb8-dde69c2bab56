@echo off
title SpectraSpeak Launcher
color 0A

echo ========================================
echo    SpectraSpeak Launcher v2.0
echo    Intelligent Spectroscopic Analysis
echo ========================================
echo.

REM Check if Python is installed
python --version >NUL 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Error: Python is not installed or not in PATH!
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

REM Activate virtual environment
echo Activating virtual environment...
call .venv\Scripts\activate.bat

REM Check if requirements are installed
echo Checking dependencies...
python -c "import streamlit, pandas, numpy, sklearn, openpyxl, seaborn" 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo Installing required packages...
    pip install -r requirements.txt
    if %ERRORLEVEL% NEQ 0 (
        echo Failed to install packages. Please check requirements.txt
        pause
        exit /b 1
    )
    echo Dependencies installed successfully!
) else (
    echo All dependencies are already installed.
)

REM Check if the main app exists
if not exist "main.py" (
    echo Error: main.py not found!
    echo Please ensure you have the latest version of SpectraSpeak.
    pause
    exit /b 1
)

echo Starting SpectraSpeak...
echo Application will open in your default browser.
echo Close this window to stop the application.
echo ========================================

REM Start the application
python -m streamlit run main.py --server.port 8527

if %ERRORLEVEL% NEQ 0 (
    echo Failed to start the application.
    echo Please check that all dependencies are installed correctly.
    echo Try running: pip install -r requirements.txt
    pause
)

echo Application stopped.
pause
