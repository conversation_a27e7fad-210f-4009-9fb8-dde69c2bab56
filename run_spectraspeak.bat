@echo off
title SpectraSpeak - Intelligent Spectroscopic Analysis Platform
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        SpectraSpeak                         ║
echo ║              Intelligent Spectroscopic Analysis             ║
echo ║                         v2.0.0                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is installed
echo [1/5] Checking Python installation...
python --version >NUL 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Error: Python is not installed or not in PATH!
    echo.
    echo Please install Python 3.8 or higher from:
    echo https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
)
echo ✅ Python found

REM Check if virtual environment exists, create if not
echo [2/5] Setting up virtual environment...
if not exist ".venv" (
    echo Creating virtual environment...
    python -m venv .venv
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
call .venv\Scripts\activate.bat
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)
echo ✅ Virtual environment activated

REM Check and install dependencies
echo [3/5] Checking dependencies...
python -c "import streamlit, pandas, numpy, sklearn, plotly, seaborn" 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo Installing required packages...
    pip install --upgrade pip
    pip install -r requirements.txt
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Failed to install packages
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
) else (
    echo ✅ All dependencies are ready
)

REM Check if the main app exists
echo [4/5] Verifying application files...
if not exist "main.py" (
    echo ❌ Error: main.py not found!
    echo Please ensure you have the complete SpectraSpeak installation.
    pause
    exit /b 1
)
echo ✅ Application files verified

REM Start the application
echo [5/5] Starting SpectraSpeak...
echo.
echo 🚀 Launching SpectraSpeak...
echo 🌐 The application will open in your default browser
echo 📊 Ready for spectroscopic analysis!
echo.
echo ⚠️  Keep this window open while using SpectraSpeak
echo ❌ Close this window to stop the application
echo.

REM Start the application with error handling
python -m streamlit run main.py --server.port 8527 --server.headless true

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Failed to start SpectraSpeak
    echo.
    echo Troubleshooting steps:
    echo 1. Check your internet connection
    echo 2. Ensure all files are present
    echo 3. Try running: pip install -r requirements.txt
    echo 4. Contact support if the problem persists
    echo.
    pause
)

echo.
echo 👋 SpectraSpeak has been closed
echo Thank you for using SpectraSpeak!
pause
