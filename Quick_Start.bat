@echo off
title SpectraSpeak Quick Start
color 0A

echo.
echo SpectraSpeak Quick Start
echo ========================
echo.
echo Starting SpectraSpeak...
echo (Assumes environment is already set up)
echo.

REM Activate virtual environment if it exists
if exist ".venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call .venv\Scripts\activate.bat
) else (
    echo Using system Python (virtual environment not found)
)

echo.
echo Launching SpectraSpeak...
echo Browser will open at: http://localhost:8527
echo.
echo IMPORTANT: Keep this window open while using SpectraSpeak!
echo.

REM Start the application
python -m streamlit run main.py --server.port 8527

echo.
echo SpectraSpeak has been closed.
pause
