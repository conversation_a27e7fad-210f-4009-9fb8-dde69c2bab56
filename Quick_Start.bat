@echo off
title SpectraSpeak Quick Start
color 0A

echo.
echo SpectraSpeak Quick Start
echo ========================
echo.
echo Starting SpectraSpeak...
echo (Assumes environment is already set up)
echo.

REM Activate virtual environment if it exists
if exist ".venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call .venv\Scripts\activate.bat
) else (
    echo Using system Python (virtual environment not found)
)



echo.
echo SpectraSpeak has been closed.
pause
