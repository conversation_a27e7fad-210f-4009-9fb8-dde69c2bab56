@echo off
title SpectraSpeak Quick Start
color 0B

echo.
echo ⚡ SpectraSpeak Quick Start
echo.
echo 🚀 Starting application...
echo (Assumes environment is already set up)
echo.

REM Quick start - assumes everything is already set up
if exist ".venv\Scripts\activate.bat" (
    call .venv\Scripts\activate.bat
) else (
    echo ⚠️  Virtual environment not found. Using system Python.
)

python -m streamlit run main.py --server.port 8527 --server.headless true

echo.
echo 👋 SpectraSpeak closed
pause
