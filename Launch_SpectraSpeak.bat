@echo off
title SpectraSpeak - Intelligent Spectroscopic Analysis Platform
color 0A

echo.
echo ================================================================
echo                        SpectraSpeak
echo              Intelligent Spectroscopic Analysis
echo                         v2.0.0
echo ================================================================
echo.

REM Check if Python is installed
echo [1/5] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH!
    echo.
    echo Please install Python 3.8 or higher from:
    echo https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation.
    echo.
    pause
    exit /b 1
)
echo [OK] Python found

REM Check if virtual environment exists, create if not
echo [2/5] Setting up virtual environment...
if not exist ".venv" (
    echo Creating virtual environment...
    python -m venv .venv
    if errorlevel 1 (
        echo [ERROR] Failed to create virtual environment
        echo.
        pause
        exit /b 1
    )
    echo [OK] Virtual environment created
) else (
    echo [OK] Virtual environment ready
)

REM Activate virtual environment
echo [3/5] Activating virtual environment...
if exist ".venv\Scripts\activate.bat" (
    call .venv\Scripts\activate.bat
    echo [OK] Virtual environment activated
) else (
    echo [WARNING] Using system Python
)

REM Check and install dependencies
echo [4/5] Checking dependencies...
python -c "import streamlit" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages (this may take a few minutes)...
    pip install --upgrade pip --quiet
    pip install -r requirements.txt
    if errorlevel 1 (
        echo [ERROR] Failed to install packages
        echo Please check your internet connection and try again
        echo.
        pause
        exit /b 1
    )
    echo [OK] Dependencies installed successfully
) else (
    echo [OK] All dependencies ready
)

REM Check if main application exists
echo [5/5] Verifying application files...
if not exist "main.py" (
    echo [ERROR] main.py not found!
    echo Please ensure you have the complete SpectraSpeak installation.
    echo.
    pause
    exit /b 1
)
echo [OK] Application files verified

REM Start the application
echo.
echo ================================================================
echo                    Starting SpectraSpeak
echo ================================================================
echo.
echo * The application will open in your default web browser
echo * Keep this window open while using SpectraSpeak
echo * Close this window to stop the application
echo * If browser doesn't open, go to: http://localhost:8527
echo.

REM Start Streamlit application with port handling
echo Attempting to start on port 8527...
python -m streamlit run main.py --server.port 8527 2>nul
if errorlevel 1 (
    echo.
    echo [WARNING] Port 8527 is in use. Trying alternative port 8528...
    python -m streamlit run main.py --server.port 8528 2>nul
    if errorlevel 1 (
        echo.
        echo [WARNING] Port 8528 is in use. Trying alternative port 8529...
        python -m streamlit run main.py --server.port 8529 2>nul
        if errorlevel 1 (
            echo.
            echo [ERROR] Multiple ports are in use.
            echo Please close any existing SpectraSpeak instances and try again.
            echo.
            echo To manually close existing instances:
            echo 1. Press Ctrl+Alt+Delete and open Task Manager
            echo 2. Look for "python.exe" processes and end them
            echo 3. Try running SpectraSpeak again
            echo.
            pause
            exit /b 1
        ) else (
            echo [OK] Started on port 8529 - Browser will open at http://localhost:8529
        )
    ) else (
        echo [OK] Started on port 8528 - Browser will open at http://localhost:8528
    )
) else (
    echo [OK] Started on port 8527 - Browser will open at http://localhost:8527
)

REM Application closed
echo.
echo ================================================================
echo SpectraSpeak has been closed. Thank you for using SpectraSpeak!
echo ================================================================
echo.
pause
