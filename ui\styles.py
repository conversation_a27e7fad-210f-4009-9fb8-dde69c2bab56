"""
Centralized Styling System for SpectraSpeak

This module contains all CSS styling and theme configurations,
extracted from the main application for better maintainability.
"""

from config.settings import (
    FONT_FAMILY, APP_TITLE_FONT, APP_TITLE_SIZE, APP_SUBTITLE_SIZE,
    STEP_TITLE_SIZE, BUTTON_FONT_SIZE, UI_COLORS, NAV_COLORS
)


class ThemeManager:
    """Manages theme variables and styling configurations."""

    @staticmethod
    def get_font_imports() -> str:
        """Get font import statements."""
        return """
        @import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Fira+Code:wght@300;400;500;600;700&display=swap');
        """

    @staticmethod
    def get_global_font_settings() -> str:
        """Get global font configuration."""
        return f"""
        /* Global Font Settings - Nunito Sans Optimized */
        html, body, [class*="css"] {{
            font-family: '{FONT_FAMILY}', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            font-feature-settings: 'kern', 'liga';
            font-variant-numeric: tabular-nums;
        }}

        /* Optimize font rendering */
        * {{
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }}

        /* Ensure all text uses Nunito Sans */
        * {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        /* Exception for code elements */
        code, pre, .stCode, .stCodeBlock {{
            font-family: 'Fira Code', 'Consolas', 'Monaco', monospace !important;
        }}
        """

    @staticmethod
    def get_header_styles() -> str:
        """Get header styling."""
        return f"""
        /* Enhanced Header with Spectroscopy Theme */
        .main-header {{
            font-family: '{APP_TITLE_FONT}', sans-serif;
            text-align: center;
            padding: 3rem 0;
            margin-bottom: 2rem;
            margin-top: -1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 70%, #4facfe 100%);
            border-radius: 0 0 30px 30px;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
            border-bottom: 6px solid rgba(255,255,255,0.3);
            position: relative;
            overflow: hidden;
        }}

        .main-header::after {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
            animation: shimmer 4s ease-in-out infinite;
        }}

        @keyframes shimmer {{
            0%, 100% {{ opacity: 0.3; }}
            50% {{ opacity: 0.7; }}
        }}

        .main-header::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, {UI_COLORS['primary']} 0%, {UI_COLORS['secondary']} 50%, {UI_COLORS['accent']} 100%);
        }}

        .header-content {{
            position: relative;
            z-index: 2;
        }}

        .title-container {{
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 0.5rem;
        }}

        .main-title {{
            font-size: {APP_TITLE_SIZE};
            font-weight: 900;
            color: white;
            margin: 0;
            line-height: 1.1;
            letter-spacing: 0.05em;
            text-shadow: 2px 2px 8px rgba(0,0,0,0.3);
            position: relative;
            z-index: 3;
        }}

        .subtitle-container {{
            margin: 0.8rem 0 0.5rem 0;
        }}

        .main-subtitle {{
            font-size: {APP_SUBTITLE_SIZE};
            font-weight: 600;
            color: rgba(255,255,255,0.95);
            margin: 0;
            line-height: 1.2;
            letter-spacing: 0.02em;
            text-shadow: 1px 1px 4px rgba(0,0,0,0.2);
            position: relative;
            z-index: 3;
        }}

        .main-version {{
            font-size: 1rem;
            font-weight: 500;
            color: rgba(255,255,255,0.9);
            margin: 0.5rem 0 0 0;
            line-height: 1;
            background: rgba(255,255,255,0.2);
            padding: 0.4rem 1.2rem;
            border-radius: 20px;
            display: inline-block;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 3;
        }}
        """

    @staticmethod
    def get_logo_styles() -> str:
        """Get logo styling."""
        return f"""
        .spectro-logo {{
            width: 80px;
            height: 60px;
            background: linear-gradient(135deg, {UI_COLORS['primary']} 0%, {UI_COLORS['secondary']} 100%);
            border-radius: 8px;
            position: relative;
            box-shadow: 0 4px 12px rgba(30, 136, 229, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }}

        .spectro-logo::before {{
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg,
                transparent 0%,
                rgba(255,255,255,0.1) 10%,
                rgba(255,255,255,0.3) 20%,
                rgba(255,255,255,0.5) 30%,
                rgba(255,255,255,0.7) 40%,
                rgba(255,255,255,0.9) 50%,
                rgba(255,255,255,0.7) 60%,
                rgba(255,255,255,0.5) 70%,
                rgba(255,255,255,0.3) 80%,
                rgba(255,255,255,0.1) 90%,
                transparent 100%);
            border-radius: 4px;
        }}

        .spectro-logo::after {{
            content: '';
            position: absolute;
            bottom: 8px;
            left: 8px;
            right: 8px;
            height: 20px;
            background: linear-gradient(90deg,
                {UI_COLORS['accent']} 0%,
                {UI_COLORS['primary']} 25%,
                {UI_COLORS['secondary']} 50%,
                #FFD700 75%,
                #FF6B6B 100%);
            border-radius: 2px;
            opacity: 0.8;
        }}

        .spectrum-text {{
            position: absolute;
            top: 8px;
            left: 8px;
            right: 8px;
            font-size: 0.6rem;
            font-weight: 600;
            color: white;
            text-align: center;
            z-index: 3;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }}

        .feature-tags {{
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 1rem 0 0.5rem 0;
            flex-wrap: wrap;
        }}

        .model-tag {{
            color: white;
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin: 0.2rem;
            display: inline-block;
            transition: all 0.3s ease;
            border: 2px solid rgba(255,255,255,0.3);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }}

        .model-tag::before {{
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }}

        .model-tag:hover::before {{
            left: 100%;
        }}

        .model-tag:hover {{
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        }}

        .model-tag.tag-1 {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }}

        .model-tag.tag-2 {{
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
        }}

        .model-tag.tag-3 {{
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }}
        """


class ComponentStyles:
    """Styling for UI components."""

    @staticmethod
    def get_step_styles() -> str:
        """Get step-specific styling."""
        return f"""
        /* Step Headers - Left Aligned */
        .step-header {{
            font-family: '{FONT_FAMILY}', sans-serif;
            font-size: {STEP_TITLE_SIZE};
            font-weight: 700;
            color: {UI_COLORS['text']};
            text-align: left;
            border-left: 5px solid {UI_COLORS['primary']};
            padding-left: 1.5rem;
            margin: 1.5rem 0;
            background: linear-gradient(90deg, rgba(30,136,229,0.1) 0%, rgba(255,255,255,0) 100%);
            padding-top: 1rem;
            padding-bottom: 1rem;
        }}

        /* All text content left-aligned */
        .main .block-container {{
            text-align: left;
        }}
        """

    @staticmethod
    def get_navigation_styles() -> str:
        """Get navigation styling."""
        return f"""
        /* Enhanced Navigation Buttons */
        .nav-button {{
            font-family: '{FONT_FAMILY}', sans-serif;
            font-size: {BUTTON_FONT_SIZE};
            font-weight: 600;
            border-radius: 25px;
            border: 2px solid transparent;
            padding: 0.75rem 1.5rem;
            margin: 0.25rem 0;
            transition: all 0.3s ease;
            cursor: pointer;
            width: 100%;
            text-align: left;
            display: flex;
            align-items: center;
        }}

        .nav-button.current {{
            background: linear-gradient(135deg, {NAV_COLORS['current']} 0%, {UI_COLORS['secondary']} 100%);
            color: white;
            border-color: {NAV_COLORS['current']};
            box-shadow: 0 4px 15px rgba(30,136,229,0.3);
            transform: translateX(5px);
        }}

        .nav-button.completed {{
            background: linear-gradient(135deg, {NAV_COLORS['completed']} 0%, {UI_COLORS['accent']} 100%);
            color: white;
            border-color: {NAV_COLORS['completed']};
        }}

        .nav-button.available {{
            background: {UI_COLORS['background']};
            color: {NAV_COLORS['available']};
            border-color: {NAV_COLORS['available']};
        }}

        .nav-button.locked {{
            background: #f8f9fa;
            color: {NAV_COLORS['locked']};
            border-color: {NAV_COLORS['locked']};
            cursor: not-allowed;
            opacity: 0.6;
        }}

        .nav-button:hover:not(.locked) {{
            transform: translateX(8px) translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
            border-color: {NAV_COLORS['hover']};
        }}
        """


class StreamlitStyles:
    """Streamlit-specific component styling."""

    @staticmethod
    def get_button_styles() -> str:
        """Get button styling for Streamlit components."""
        return f"""
        /* Enhanced Buttons - Consistent Navigation Styling */
        .stButton > button {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
            font-size: {BUTTON_FONT_SIZE} !important;
            font-weight: 600 !important;
            border-radius: 25px !important;
            border: 2px solid transparent !important;
            padding: 0.75rem 1.5rem !important;
            margin: 0.25rem 0 !important;
            transition: all 0.3s ease !important;
            cursor: pointer !important;
            width: 100% !important;
            text-align: left !important;
            display: flex !important;
            align-items: center !important;
            background: {UI_COLORS['background']} !important;
            color: {NAV_COLORS['available']} !important;
            border-color: {NAV_COLORS['available']} !important;
        }}

        /* Primary button (current step) */
        .stButton > button[kind="primary"] {{
            background: linear-gradient(135deg, {NAV_COLORS['current']} 0%, {UI_COLORS['secondary']} 100%) !important;
            color: white !important;
            border-color: {NAV_COLORS['current']} !important;
            box-shadow: 0 4px 15px rgba(30,136,229,0.3) !important;
            transform: translateX(5px) !important;
            font-size: {BUTTON_FONT_SIZE} !important;
        }}

        /* Secondary button (completed step) */
        .stButton > button[kind="secondary"] {{
            background: linear-gradient(135deg, {NAV_COLORS['completed']} 0%, {UI_COLORS['accent']} 100%) !important;
            color: white !important;
            border-color: {NAV_COLORS['completed']} !important;
            font-size: {BUTTON_FONT_SIZE} !important;
        }}

        /* Tertiary button (available step) */
        .stButton > button[kind="tertiary"] {{
            background: {UI_COLORS['background']} !important;
            color: {NAV_COLORS['available']} !important;
            border-color: {NAV_COLORS['available']} !important;
            font-size: {BUTTON_FONT_SIZE} !important;
        }}

        .stButton > button:hover {{
            transform: translateX(8px) translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(0,0,0,0.15) !important;
            border-color: {NAV_COLORS['hover']} !important;
        }}

        /* Ensure primary button hover maintains current step styling */
        .stButton > button[kind="primary"]:hover {{
            background: linear-gradient(135deg, {NAV_COLORS['hover']} 0%, {UI_COLORS['accent']} 100%) !important;
            transform: translateX(8px) translateY(-2px) !important;
        }}

        /* Fix button focus and active states to prevent black boxes */
        .stButton > button:focus {{
            outline: none !important;
            box-shadow: 0 0 0 2px rgba(30, 136, 229, 0.3) !important;
            border-color: #1e88e5 !important;
        }}

        .stButton > button:active {{
            transform: translateY(1px) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }}

        /* Remove any pseudo-elements that might cause black boxes */
        .stButton > button::before,
        .stButton > button::after {{
            display: none !important;
            content: none !important;
        }}

        /* Ensure no black backgrounds on any button state */
        .stButton > button,
        .stButton > button:hover,
        .stButton > button:active,
        .stButton > button:focus {{
            background-color: transparent !important;
            background-image: none !important;
        }}

        /* Restore proper button backgrounds with specificity */
        .stButton > button[kind="primary"] {{
            background: linear-gradient(135deg, {NAV_COLORS['current']} 0%, {UI_COLORS['secondary']} 100%) !important;
            color: white !important;
        }}

        .stButton > button[kind="secondary"] {{
            background: linear-gradient(135deg, {NAV_COLORS['completed']} 0%, {UI_COLORS['accent']} 100%) !important;
            color: white !important;
        }}
        """

    @staticmethod
    def get_component_styles() -> str:
        """Get styling for various Streamlit components."""
        return f"""
        /* Sidebar Styling */
        .css-1d391kg {{
            background: linear-gradient(180deg, {UI_COLORS['sidebar']} 0%, #e9ecef 100%);
        }}

        /* Metric Cards */
        .metric-card {{
            background: linear-gradient(135deg, {UI_COLORS['primary']} 0%, {UI_COLORS['secondary']} 100%);
            padding: 1.5rem;
            border-radius: 15px;
            color: white;
            text-align: center;
            margin: 0.75rem 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }}

        /* Success/Warning Boxes */
        .success-box {{
            background: linear-gradient(135deg, {UI_COLORS['success']} 0%, #4caf50 100%);
            padding: 1.5rem;
            border-radius: 15px;
            color: white;
            margin: 1rem 0;
            box-shadow: 0 4px 15px rgba(44,160,44,0.2);
        }}

        .warning-box {{
            background: linear-gradient(135deg, {UI_COLORS['warning']} 0%, #e74c3c 100%);
            padding: 1.5rem;
            border-radius: 15px;
            color: white;
            margin: 1rem 0;
            box-shadow: 0 4px 15px rgba(214,39,40,0.2);
        }}

        /* File Uploader Styling */
        .stFileUploader > div > div {{
            border: 2px dashed {UI_COLORS['primary']};
            border-radius: 15px;
            padding: 2rem;
            background: linear-gradient(135deg, rgba(30,136,229,0.05) 0%, rgba(66,165,245,0.05) 100%);
        }}

        /* Fix file uploader button black box issues */
        .stFileUploader button,
        .stFileUploader button:focus,
        .stFileUploader button:active,
        .stFileUploader button:hover {{
            background: transparent !important;
            border: 2px solid {UI_COLORS['primary']} !important;
            color: {UI_COLORS['primary']} !important;
            outline: none !important;
            box-shadow: none !important;
            border-radius: 8px !important;
            padding: 0.5rem 1rem !important;
            font-family: '{FONT_FAMILY}', sans-serif !important;
            font-weight: 500 !important;
            transition: all 0.3s ease !important;
        }}

        .stFileUploader button:hover {{
            background: rgba(30, 136, 229, 0.1) !important;
            border-color: {UI_COLORS['secondary']} !important;
            color: {UI_COLORS['secondary']} !important;
        }}

        .stFileUploader button:active {{
            background: rgba(30, 136, 229, 0.2) !important;
            transform: translateY(1px) !important;
        }}

        /* Remove pseudo-elements from file uploader buttons */
        .stFileUploader button::before,
        .stFileUploader button::after {{
            display: none !important;
            content: none !important;
        }}

        /* Tab Styling */
        .stTabs [data-baseweb="tab-list"] {{
            gap: 8px;
        }}

        .stTabs [data-baseweb="tab"] {{
            font-family: '{FONT_FAMILY}', sans-serif;
            font-weight: 600;
            border-radius: 15px 15px 0 0;
            padding: 1rem 2rem;
            background: linear-gradient(135deg, {UI_COLORS['sidebar']} 0%, #dee2e6 100%);
        }}

        .stTabs [aria-selected="true"] {{
            background: linear-gradient(135deg, {UI_COLORS['primary']} 0%, {UI_COLORS['secondary']} 100%);
            color: white;
        }}

        /* Progress Bar */
        .stProgress > div > div > div > div {{
            background: linear-gradient(90deg, {UI_COLORS['primary']} 0%, {UI_COLORS['secondary']} 100%);
        }}

        /* Expander Styling */
        .streamlit-expanderHeader {{
            font-family: '{FONT_FAMILY}', sans-serif;
            font-weight: 600;
            background: linear-gradient(135deg, {UI_COLORS['sidebar']} 0%, #dee2e6 100%);
            border-radius: 10px;
        }}
        """

    @staticmethod
    def get_comprehensive_component_styles() -> str:
        """Get comprehensive styling for all Streamlit components."""
        return f"""
        /* Comprehensive Streamlit Component Font Styling */
        .stSelectbox > div > div {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stTextInput > div > div > input {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stTextArea > div > div > textarea {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stNumberInput > div > div > input {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stSlider > div > div > div {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stCheckbox > label {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stRadio > div {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stMultiSelect > div {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stDataFrame {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stTable {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stMetric {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stAlert {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stInfo, .stSuccess, .stWarning, .stError {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stMarkdown {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        /* Sidebar components */
        .css-1d391kg .stSelectbox > div > div {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .css-1d391kg .stButton > button {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .css-1d391kg .stMarkdown {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        /* File uploader */
        .stFileUploader {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        .stFileUploader > div > div > div {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        /* Plotly charts */
        .js-plotly-plot {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        /* Headers and text elements */
        h1, h2, h3, h4, h5, h6 {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}

        p, span, div, label {{
            font-family: '{FONT_FAMILY}', sans-serif !important;
        }}
        """


class ResponsiveStyles:
    """Responsive design styles for different screen sizes."""

    @staticmethod
    def get_responsive_styles() -> str:
        """Get responsive design CSS."""
        return """
        /* Responsive Design for Header */
        @media (max-width: 768px) {
            .title-container {
                flex-direction: column;
                gap: 0.5rem;
            }

            .spectro-logo {
                width: 60px;
                height: 45px;
            }

            .main-title {
                font-size: 3rem !important;
            }

            .main-subtitle {
                font-size: 1.5rem !important;
            }

            .feature-tags {
                gap: 0.5rem;
            }

            .feature-tag {
                font-size: 0.75rem;
                padding: 0.2rem 0.6rem;
            }
        }

        @media (max-width: 480px) {
            .main-header {
                padding: 1rem 0;
                margin-bottom: 1rem;
            }

            .main-title {
                font-size: 2.5rem !important;
            }

            .main-subtitle {
                font-size: 1.2rem !important;
            }

            .spectro-logo {
                width: 50px;
                height: 35px;
            }

            .spectrum-text {
                font-size: 0.5rem;
            }
        }

        /* Responsive Navigation */
        @media (max-width: 768px) {
            .nav-button {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
            }

            .stButton > button {
                padding: 0.5rem 1rem !important;
                font-size: 0.9rem !important;
            }
        }

        /* Responsive Cards and Components */
        @media (max-width: 768px) {
            .metric-card {
                padding: 1rem;
                margin: 0.5rem 0;
            }

            .success-box, .warning-box {
                padding: 1rem;
                margin: 0.5rem 0;
            }
        }

        /* Dynamic Model Card Heights */
        .model-card {
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .model-card-content {
            flex-grow: 1;
            overflow-wrap: break-word;
            word-wrap: break-word;
            hyphens: auto;
        }

        /* Flexible Grid Layout */
        .responsive-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            width: 100%;
        }

        @media (max-width: 768px) {
            .responsive-grid {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }
        }
        """


class AccessibilityStyles:
    """Accessibility-focused styling."""

    @staticmethod
    def get_accessibility_styles() -> str:
        """Get accessibility CSS."""
        return """
        /* Accessibility Improvements */

        /* Focus indicators */
        .stButton > button:focus,
        .nav-button:focus,
        .stSelectbox select:focus,
        .stTextInput input:focus,
        .stTextArea textarea:focus {
            outline: 3px solid #4A90E2;
            outline-offset: 2px;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .main-title {
                -webkit-text-fill-color: #000000;
                color: #000000;
            }

            .nav-button {
                border-width: 3px;
            }

            .feature-tag {
                border: 2px solid #000000;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .nav-button,
            .stButton > button {
                transition: none;
            }

            .nav-button:hover:not(.locked) {
                transform: none;
            }

            .stButton > button:hover {
                transform: none !important;
            }
        }

        /* Screen reader support */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* ARIA labels and descriptions */
        [aria-label]:focus::after {
            content: attr(aria-label);
            position: absolute;
            background: #333;
            color: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            z-index: 1000;
        }
        """


def get_complete_css() -> str:
    """Get the complete CSS for the application."""
    theme = ThemeManager()
    components = ComponentStyles()
    streamlit = StreamlitStyles()
    responsive = ResponsiveStyles()
    accessibility = AccessibilityStyles()

    return f"""
    <style>
    {theme.get_font_imports()}
    {theme.get_global_font_settings()}
    {theme.get_header_styles()}
    {theme.get_logo_styles()}
    {components.get_step_styles()}
    {components.get_navigation_styles()}
    {streamlit.get_button_styles()}
    {streamlit.get_component_styles()}
    {streamlit.get_comprehensive_component_styles()}
    {responsive.get_responsive_styles()}
    {accessibility.get_accessibility_styles()}
    </style>
    """
