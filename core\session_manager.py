"""
Session Manager for MELK Chemo Copilot

Handles all session state operations with validation and consistency checks.
"""

import streamlit as st
from typing import Any, Dict, List, Optional
import pandas as pd


class SessionManager:
    """Manages session state for the MELK Chemo Copilot application."""

    def __init__(self):
        self._initialize_session_state()

    def _initialize_session_state(self) -> None:
        """Initialize session state with default values."""
        if "current_step" not in st.session_state:
            st.session_state.current_step = 1

        if "workflow_initialized" not in st.session_state:
            st.session_state.workflow_initialized = True

    def get(self, key: str, default: Any = None) -> Any:
        """Get a value from session state."""
        return st.session_state.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """Set a value in session state."""
        st.session_state[key] = value

    def has(self, key: str) -> bool:
        """Check if a key exists in session state."""
        return key in st.session_state

    def remove(self, key: str) -> None:
        """Remove a key from session state."""
        if key in st.session_state:
            del st.session_state[key]

    def get_current_step(self) -> int:
        """Get the current workflow step."""
        return st.session_state.get("current_step", 1)

    def set_current_step(self, step: int) -> None:
        """Set the current workflow step."""
        if 1 <= step <= 7:
            st.session_state.current_step = step
        else:
            raise ValueError(f"Invalid step number: {step}")

    def get_data_summary(self) -> Dict[str, Any]:
        """Get a summary of loaded data."""
        summary = {}

        # Check for training data
        if self.has("x_train"):
            x_train = self.get("x_train")
            summary["x_train_shape"] = x_train.shape if hasattr(x_train, 'shape') else "Unknown"

        if self.has("y_train"):
            y_train = self.get("y_train")
            summary["y_train_shape"] = y_train.shape if hasattr(y_train, 'shape') else "Unknown"

        # Check for test data
        if self.has("x_test"):
            x_test = self.get("x_test")
            summary["x_test_shape"] = x_test.shape if hasattr(x_test, 'shape') else "Unknown"

        if self.has("y_test"):
            y_test = self.get("y_test")
            summary["y_test_shape"] = y_test.shape if hasattr(y_test, 'shape') else "Unknown"

        # Check for workflow progress
        summary["current_step"] = self.get_current_step()
        summary["preprocessing_method"] = self.get("best_method", "Not selected")
        summary["pls_algorithm"] = self.get("pls_algorithm", "Not selected")
        summary["cv_method"] = self.get("best_cv_method", "Not selected")

        return summary

    def validate_data_consistency(self) -> List[str]:
        """Validate data consistency across the workflow."""
        issues = []

        # Check training data consistency
        if self.has("x_train") and self.has("y_train"):
            x_train = self.get("x_train")
            y_train = self.get("y_train")

            if hasattr(x_train, 'shape') and hasattr(y_train, 'shape'):
                if x_train.shape[0] != y_train.shape[0]:
                    issues.append("Training data sample count mismatch")

        # Check test data consistency
        if self.has("x_test") and self.has("y_test"):
            x_test = self.get("x_test")
            y_test = self.get("y_test")

            if hasattr(x_test, 'shape') and hasattr(y_test, 'shape'):
                if x_test.shape[0] != y_test.shape[0]:
                    issues.append("Test data sample count mismatch")

        # Check variable consistency between training and test
        if self.has("x_train") and self.has("x_test"):
            x_train = self.get("x_train")
            x_test = self.get("x_test")

            if hasattr(x_train, 'shape') and hasattr(x_test, 'shape'):
                if x_train.shape[1] != x_test.shape[1]:
                    issues.append("Variable count mismatch between training and test data")

        return issues

    def clear_step_data(self, step: int) -> None:
        """Clear data specific to a workflow step."""
        step_data_keys = {
            1: ["x_train", "y_train", "x_test", "y_test"],
            2: [],  # Data overview doesn't store specific data
            3: ["preprocessing_complete", "x_train_preprocessed", "x_test_preprocessed", "best_method", "preprocessing_options"],
            4: ["variable_selection_applied", "variable_selection_results", "selected_variables", "x_train_selected", "x_test_selected"],
            5: ["model_selection_complete", "grid_search_results", "best_model_config"],
            6: ["cv_method_applied", "cv_results", "best_cv_method", "optimal_lv", "min_rmsecv"],
            7: ["workflow_completed", "final_model"]
        }

        keys_to_remove = step_data_keys.get(step, [])
        for key in keys_to_remove:
            self.remove(key)

    def reset_workflow(self) -> None:
        """Reset the entire workflow."""
        # Clear all workflow-related data
        workflow_keys = [
            "x_train", "y_train", "x_test", "y_test",
            "best_method", "preprocessing_options",
            "pls_algorithm", "pls_algorithm_config",
            "cv_results", "cv_method_applied", "best_cv_method", "optimal_lv", "min_rmsecv",
            "variable_selection_completed", "selected_variables",
            "workflow_completed", "final_model"
        ]

        for key in workflow_keys:
            self.remove(key)

        # Reset to step 1
        self.set_current_step(1)

    def get_workflow_progress(self) -> Dict[str, bool]:
        """Get the completion status of each workflow step."""
        return {
            "step_1": self.has("x_train") and self.has("y_train"),
            "step_2": True,  # Data overview doesn't require completion
            "step_3": self.has("preprocessing_complete") and self.get("preprocessing_complete", False),
            "step_4": self.has("variable_selection_applied") and self.get("variable_selection_applied", False),
            "step_5": self.has("model_selection_complete") and self.get("model_selection_complete", False),
            "step_6": self.has("cv_method_applied") and self.get("cv_method_applied", False),
            "step_7": self.has("workflow_completed") and self.get("workflow_completed", False)
        }

    def can_access_step(self, step: int) -> bool:
        """Check if a step can be accessed based on prerequisites."""
        progress = self.get_workflow_progress()

        # Step 1 is always accessible
        if step == 1:
            return True

        # Step 2 requires step 1 completion
        if step == 2:
            return progress["step_1"]

        # Step 3 requires step 1 completion (step 2 is optional)
        if step == 3:
            return progress["step_1"]

        # Step 4 (Variable Selection) requires step 3 (Preprocessing) completion
        if step == 4:
            return progress["step_3"]

        # Step 5 (Model Selection) requires step 4 (Variable Selection) completion
        if step == 5:
            return progress["step_4"]

        # Step 6 (Model Prediction) requires step 5 (Model Selection) completion
        if step == 6:
            return progress["step_5"]

        # Step 7 (Model Report) requires step 6 (Model Prediction) completion
        if step == 7:
            return progress["step_6"]

        return False


# Create a global session manager instance
session_manager = SessionManager()
