"""
Step 2: Data Overview for SpectraSpeak
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Optional
from steps.base_step import BaseStep


class DataOverviewStep(BaseStep):
    """Step 2: Data Overview and Visualization"""

    def __init__(self):
        super().__init__(2, "Data Overview")

    def render(self) -> None:
        """Render the data overview step."""
        self.render_header()

        # Check if required data is available
        if not self.session.has("x_train") or not self.session.has("y_train"):
            st.warning("⚠️ Please upload training data in Step 1 before proceeding.")
            if st.button("← Back to Step 1"):
                self.session.set_current_step(1)
                st.rerun()
            return

        # Get data from session
        x_train = self.session.get("x_train")
        y_train = self.session.get("y_train")
        x_test = self.session.get("x_test")
        y_test = self.session.get("y_test")

        # Create tabs for different overview sections
        tab1, tab2, tab3, tab4 = st.tabs([
            "📊 Dataset Summary",
            "📋 Data Tables",
            "📈 Spectra Visualization",
            "🔗 Correlation Analysis"
        ])

        with tab1:
            self._render_dataset_summary(x_train, y_train, x_test, y_test)

        with tab2:
            self._render_data_tables(x_train, y_train, x_test, y_test)

        with tab3:
            self._render_spectra_visualization(x_train, x_test)

        with tab4:
            self._render_correlation_analysis(x_train, y_train, x_test, y_test)

        # Navigation section
        self._render_navigation_section()

    def _render_dataset_summary(self, x_train: pd.DataFrame, y_train: pd.DataFrame,
                               x_test: Optional[pd.DataFrame], y_test: Optional[pd.DataFrame]) -> None:
        """Render dataset summary with metrics and statistics."""
        st.markdown("### 📊 Dataset Summary")

        # Create summary cards
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### Training Data")
            self._create_data_summary_card(x_train, "X_train Dataset", "#1E88E5")
            self._create_data_summary_card(y_train, "Y_train Dataset", "#43A047")

        with col2:
            st.markdown("#### Test Data")
            if x_test is not None:
                self._create_data_summary_card(x_test, "X_test Dataset", "#E53935")
            else:
                st.info("No X_test data available")

            if y_test is not None:
                self._create_data_summary_card(y_test, "Y_test Dataset", "#FFA000")
            else:
                st.info("No Y_test data available")

        # Data quality checks
        st.markdown("---")
        st.markdown("### 🔍 Data Quality Checks")

        quality_checks = self._perform_data_quality_checks(x_train, y_train, x_test, y_test)

        for check, result in quality_checks.items():
            if result:
                st.success(f"✅ {check}")
            else:
                st.error(f"❌ {check}")

    def _create_data_summary_card(self, data: pd.DataFrame, title: str, color: str) -> None:
        """Create a summary card for a dataset."""
        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, {color}20 0%, {color}10 100%);
            border-left: 4px solid {color};
            padding: 1rem;
            border-radius: 8px;
            margin: 0.5rem 0;
        ">
            <h4 style="color: {color}; margin: 0 0 0.5rem 0;">{title}</h4>
            <p style="margin: 0.2rem 0;"><strong>Shape:</strong> {data.shape[0]} × {data.shape[1]}</p>
            <p style="margin: 0.2rem 0;"><strong>Memory:</strong> {data.memory_usage(deep=True).sum() / 1024:.1f} KB</p>
            <p style="margin: 0.2rem 0;"><strong>Data Type:</strong> {data.dtypes.value_counts().to_dict()}</p>
        </div>
        """, unsafe_allow_html=True)

    def _perform_data_quality_checks(self, x_train: pd.DataFrame, y_train: pd.DataFrame,
                                   x_test: Optional[pd.DataFrame], y_test: Optional[pd.DataFrame]) -> dict:
        """Perform data quality checks."""
        checks = {}

        # Basic data checks
        checks["X_train has no missing values"] = not x_train.isnull().any().any()
        checks["Y_train has no missing values"] = not y_train.isnull().any().any()
        checks["X_train is numeric"] = x_train.select_dtypes(include=[np.number]).shape[1] > 0
        checks["Y_train is numeric"] = y_train.select_dtypes(include=[np.number]).shape[1] > 0
        checks["Sample alignment (X_train vs Y_train)"] = x_train.shape[0] == y_train.shape[0]

        # Test data checks if available
        if x_test is not None:
            checks["X_test has no missing values"] = not x_test.isnull().any().any()
            checks["X_test is numeric"] = x_test.select_dtypes(include=[np.number]).shape[1] > 0
            checks["Variable consistency (X_test vs X_train)"] = x_test.shape[1] == x_train.shape[1]

        if y_test is not None:
            checks["Y_test has no missing values"] = not y_test.isnull().any().any()
            checks["Y_test is numeric"] = y_test.select_dtypes(include=[np.number]).shape[1] > 0
            checks["Target consistency (Y_test vs Y_train)"] = y_test.shape[1] == y_train.shape[1]

        if x_test is not None and y_test is not None:
            checks["Sample alignment (X_test vs Y_test)"] = x_test.shape[0] == y_test.shape[0]

        return checks

    def _render_data_tables(self, x_train: pd.DataFrame, y_train: pd.DataFrame,
                          x_test: Optional[pd.DataFrame], y_test: Optional[pd.DataFrame]) -> None:
        """Render data tables with all datasets."""
        st.markdown("### 📋 Data Tables")
        st.markdown("Complete view of all datasets with sample names and variable values.")

        # X_train data
        st.markdown("#### X_train Data (Spectroscopic Data)")
        st.markdown(f"**Shape:** {x_train.shape[0]} samples × {x_train.shape[1]} variables")
        st.dataframe(x_train, use_container_width=True)

        # Y_train data
        st.markdown("#### Y_train Data (Target Concentrations)")
        st.markdown(f"**Shape:** {y_train.shape[0]} samples × {y_train.shape[1]} targets")
        st.dataframe(y_train, use_container_width=True)

        # X_test data
        if x_test is not None:
            st.markdown("#### X_test Data (Test Spectroscopic Data)")
            st.markdown(f"**Shape:** {x_test.shape[0]} samples × {x_test.shape[1]} variables")
            st.dataframe(x_test, use_container_width=True)

        # Y_test data
        if y_test is not None:
            st.markdown("#### Y_test Data (Test Target Concentrations)")
            st.markdown(f"**Shape:** {y_test.shape[0]} samples × {y_test.shape[1]} targets")
            st.dataframe(y_test, use_container_width=True)

    def _render_spectra_visualization(self, x_train: pd.DataFrame, x_test: Optional[pd.DataFrame]) -> None:
        """Render spectra visualization plots."""
        st.markdown("### 📈 Spectra Visualization")

        # Create subtabs for different visualizations
        viz_tabs = st.tabs(["X_train Spectra", "X_test Spectra", "Data Heatmaps"])

        with viz_tabs[0]:
            st.markdown("#### X_train Spectra Overlay")
            fig_train = self._plot_spectra_overlay(x_train, "Training Spectra")
            st.plotly_chart(fig_train, use_container_width=True)

        with viz_tabs[1]:
            if x_test is not None:
                st.markdown("#### X_test Spectra Overlay")
                fig_test = self._plot_spectra_overlay(x_test, "Test Spectra")
                st.plotly_chart(fig_test, use_container_width=True)
            else:
                st.info("No X_test data available for visualization")

        with viz_tabs[2]:
            st.markdown("#### Data Heatmaps")

            col1, col2 = st.columns(2)

            with col1:
                st.markdown("##### X_train Heatmap")
                fig_heatmap_train = self._plot_data_heatmap(x_train, "X_train Data Heatmap")
                st.plotly_chart(fig_heatmap_train, use_container_width=True)

            with col2:
                if x_test is not None:
                    st.markdown("##### X_test Heatmap")
                    fig_heatmap_test = self._plot_data_heatmap(x_test, "X_test Data Heatmap")
                    st.plotly_chart(fig_heatmap_test, use_container_width=True)
                else:
                    st.info("No X_test data available for heatmap")

    def _plot_spectra_overlay(self, data: pd.DataFrame, title: str) -> go.Figure:
        """Create spectra overlay plot."""
        # Get numeric data
        data_numeric = data.select_dtypes(include=[np.number])

        # Create wavelength values (assuming column names are wavelengths or use indices)
        try:
            # Try to extract wavelengths from column names
            wavelengths = [float(col.split('_')[-1]) if '_' in str(col) else float(col)
                          for col in data_numeric.columns]
        except:
            # Use column indices if wavelength extraction fails
            wavelengths = list(range(len(data_numeric.columns)))

        # Create figure
        fig = go.Figure()

        # Add traces for each sample (limit to first 20 for performance)
        colors = px.colors.qualitative.Plotly
        n_samples = min(len(data_numeric), 20)

        for i in range(n_samples):
            sample_name = str(data_numeric.index[i])
            fig.add_trace(go.Scatter(
                x=wavelengths,
                y=data_numeric.iloc[i].values,
                mode='lines',
                name=sample_name,
                line=dict(color=colors[i % len(colors)], width=1.5),
                opacity=0.7
            ))

        # Add mean spectrum
        fig.add_trace(go.Scatter(
            x=wavelengths,
            y=data_numeric.mean().values,
            mode='lines',
            name='Mean Spectrum',
            line=dict(color='black', width=3, dash='dash'),
            opacity=1
        ))

        # Update layout
        fig.update_layout(
            title=title,
            xaxis_title="Wavelength (nm)" if 'wavelength' in str(data.columns[0]).lower() else "Variable Index",
            yaxis_title="Absorbance",
            template="plotly_white",
            height=500,
            showlegend=True,
            legend=dict(
                orientation="v",
                yanchor="top",
                y=1,
                xanchor="left",
                x=1.02
            )
        )

        return fig

    def _plot_data_heatmap(self, data: pd.DataFrame, title: str) -> go.Figure:
        """Create data heatmap."""
        # Get numeric data
        data_numeric = data.select_dtypes(include=[np.number])

        # Sample data if too large
        if data_numeric.shape[1] > 100:
            step = data_numeric.shape[1] // 100
            data_numeric = data_numeric.iloc[:, ::step]

        # Create heatmap
        fig = go.Figure(data=go.Heatmap(
            z=data_numeric.values,
            x=[f"Var_{i}" for i in range(data_numeric.shape[1])],
            y=[str(idx) for idx in data_numeric.index],
            colorscale="Viridis",
            colorbar=dict(title="Value")
        ))

        # Update layout
        fig.update_layout(
            title=title,
            xaxis_title="Variables",
            yaxis_title="Samples",
            template="plotly_white",
            height=400
        )

        return fig

    def _render_correlation_analysis(self, x_train: pd.DataFrame, y_train: pd.DataFrame,
                                   x_test: Optional[pd.DataFrame], y_test: Optional[pd.DataFrame]) -> None:
        """Render correlation analysis."""
        st.markdown("### 🔗 Correlation Analysis")

        # Y_train correlation matrix
        st.markdown("#### Y_train Correlation Matrix")
        y_numeric = y_train.select_dtypes(include=[np.number])

        if y_numeric.shape[1] > 1:
            corr_matrix = y_numeric.corr()

            fig_corr = go.Figure(data=go.Heatmap(
                z=corr_matrix.values,
                x=corr_matrix.columns,
                y=corr_matrix.columns,
                colorscale="RdBu",
                zmid=0,
                colorbar=dict(title="Correlation")
            ))

            fig_corr.update_layout(
                title="Y_train Variables Correlation",
                template="plotly_white",
                height=400
            )

            st.plotly_chart(fig_corr, use_container_width=True)
        else:
            st.info("Only one target variable available - no correlation matrix needed")

        # Basic statistics
        st.markdown("#### Target Variables Statistics")
        st.dataframe(y_numeric.describe(), use_container_width=True)

    def _render_navigation_section(self) -> None:
        """Render navigation buttons."""
        st.markdown("---")

        clicked = self.render_navigation_buttons(
            show_previous=True,
            next_enabled=True,
            custom_next_text="Next: Preprocessing →"
        )

        self.handle_navigation(clicked)

    def validate_step_completion(self) -> bool:
        """Validate that data overview is complete."""
        return self.session.has("x_train") and self.session.has("y_train")

    def check_prerequisites(self) -> list:
        """Check prerequisites for this step."""
        issues = []
        if not self.session.has("x_train"):
            issues.append("X_train data is required")
        if not self.session.has("y_train"):
            issues.append("Y_train data is required")
        return issues
