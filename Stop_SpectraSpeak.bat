@echo off
title Stop SpectraSpeak
color 0C

echo.
echo ================================================================
echo                      Stop SpectraSpeak                      
echo ================================================================
echo.

echo Stopping all SpectraSpeak instances...
echo.

REM Kill any Python processes running Streamlit
echo Looking for SpectraSpeak processes...
tasklist /FI "IMAGENAME eq python.exe" /FO TABLE | findstr python.exe >nul
if errorlevel 1 (
    echo [INFO] No Python processes found running
) else (
    echo [INFO] Found Python processes. Attempting to stop SpectraSpeak...
    
    REM Try to kill processes gracefully first
    for /f "tokens=2" %%i in ('tasklist /FI "IMAGENAME eq python.exe" /FO CSV ^| findstr "python.exe"') do (
        echo Stopping process %%i...
        taskkill /PID %%i /T >nul 2>&1
    )
    
    REM Wait a moment
    timeout /t 2 /nobreak >nul
    
    REM Force kill if still running
    taskkill /F /IM python.exe >nul 2>&1
    taskkill /F /IM streamlit.exe >nul 2>&1
)

echo.
echo [OK] SpectraSpeak processes stopped
echo.
echo You can now start SpectraSpeak again using:
echo - Launch_SpectraSpeak.bat (full setup)
echo - Quick_Start.bat (quick launch)
echo.
echo Ports 8527, 8528, and 8529 should now be available.
echo.
pause
