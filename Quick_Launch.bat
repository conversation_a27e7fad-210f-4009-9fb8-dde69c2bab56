@echo off
title SpectraSpeak Quick Launch
color 0A

echo.
echo SpectraSpeak Quick Launch
echo ========================
echo.

echo Activating environment...
if exist ".venv\Scripts\activate.bat" (
    call .venv\Scripts\activate.bat
    echo Environment activated
) else (
    echo Using system Python
)

echo.
echo Starting SpectraSpeak...
echo Keep this window open!
echo.

python -m streamlit run main.py --server.port 8527

echo.
echo SpectraSpeak closed.
pause
