# 📖 SpectraSpeak User Guide

## 🚀 Getting Started

### First Time Setup
1. **Download** SpectraSpeak to your computer
2. **Extract** all files to a folder (e.g., `C:\SpectraSpeak\`)
3. **Double-click** `Launch_SpectraSpeak.bat`
4. **Wait** for automatic installation (first time only)
5. **<PERSON><PERSON><PERSON> opens** automatically with SpectraSpeak

### Daily Usage
- **Double-click** `Quick_Start.bat` for instant launch
- **Keep the command window open** while using SpectraSpeak
- **Close the command window** to stop the application

---

## 📊 Step-by-Step Workflow

### Step 1: Data Upload 📁
**What to do:**
- Click "Browse files" to upload your data
- Upload X_train.csv (spectral data for training)
- Upload Y_train.csv (reference values for training)
- Optionally upload X_test.csv and Y_test.csv

**Tips:**
- Use the sample data provided for testing
- Ensure your CSV files have proper headers
- Check data preview for any issues

### Step 2: Data Overview 👁️
**What you'll see:**
- Dataset statistics and summary
- Spectral plots of your data
- Correlation analysis
- Data quality indicators

**What to check:**
- Data looks correct in the plots
- No obvious outliers or errors
- Correlation patterns make sense

### Step 3: Preprocessing ⚙️
**Choose preprocessing method:**
- **Mean Centering**: Removes average spectrum
- **Autoscaling**: Standardizes each wavelength
- **SNV**: Corrects for scatter effects
- **Savitzky-Golay**: Smooths and takes derivatives

**Recommendation:**
- Use automatic optimization for best results
- SNV + Savitzky-Golay works well for most data

### Step 4: Variable Selection 🎯
**Two approaches:**
1. **Algorithmic**: Let the software choose (VIP, CARS, GA)
2. **Knowledge-based**: Choose wavelength ranges manually

**Tips:**
- Start with algorithmic selection
- Use knowledge-based for specific spectral regions
- Check the visualization of selected variables

### Step 5: Model Selection 🧠
**Algorithm categories:**
- **PLS Models**: NIPALS, SIMPLS (traditional)
- **Neural Networks**: ANN (advanced pattern recognition)
- **Support Vector**: SVR (robust to outliers)
- **Ensemble**: XGBoost (highest accuracy)

**Cross-validation:**
- K-Fold: Standard approach
- Leave-One-Out: For small datasets
- Shuffle Split: For large datasets

### Step 7: Model Report 📈
**Review results:**
- Model performance metrics (R², RMSEP)
- Predicted vs actual plots
- Residual analysis
- Download comprehensive report

---

## 💡 Tips for Best Results

### Data Preparation
- **Clean data**: Remove obvious outliers before upload
- **Consistent format**: Use same wavelength range for all samples
- **Sufficient samples**: At least 20-30 samples per analyte
- **Representative data**: Include full range of expected values

### Model Selection
- **Start simple**: Try PLS first, then advanced methods
- **Compare models**: Use the nomination system in Step 5
- **Check overfitting**: Ensure test performance matches training
- **Validate results**: Use independent test set when possible

### Troubleshooting
- **Slow performance**: Reduce data size or use fewer variables
- **Poor results**: Check data quality and preprocessing
- **Errors**: Use ChatGPT help buttons for guidance
- **Crashes**: Restart with `run_spectraspeak.bat`

---

## 📁 File Organization

```
SpectraSpeak/
├── Launch_SpectraSpeak.bat # Main launcher
├── Quick_Start.bat         # Quick launcher
├── Stop_SpectraSpeak.bat   # Stop utility
├── Fix_Port_Issues.bat     # Port troubleshooting
├── main.py                 # Application core
├── requirements.txt        # Dependencies
├── sample_data/           # Example datasets
│   ├── Trial 1/          # UV-Vis data
│   └── Trial 2/          # NIR data
├── docs/                 # Documentation
└── [other folders]       # Application files
```

---

## 🆘 Common Issues & Solutions

### "Python not found"
**Solution:** Install Python from [python.org](https://python.org)
- Download Python 3.8 or higher
- Check "Add Python to PATH" during installation
- Restart computer after installation

### "Dependencies missing"
**Solution:** Run the full launcher
- Use `Launch_SpectraSpeak.bat` instead of `Quick_Start.bat`
- Ensure internet connection for package downloads
- Wait for complete installation

### "Port 8527 is already in use"
**Solution:** Use the port fix utility
- Double-click `Fix_Port_Issues.bat` for automatic resolution
- Or double-click `Stop_SpectraSpeak.bat` to stop existing instances
- The launcher will automatically try alternative ports (8528, 8529)

### "Application won't start"
**Solution:** Check the following
- Close any other instances of SpectraSpeak
- Use `Stop_SpectraSpeak.bat` to force close existing instances
- Try restarting your computer
- Check antivirus software isn't blocking

### "Poor model performance"
**Solution:** Review your approach
- Check data quality and preprocessing
- Try different variable selection methods
- Use cross-validation to avoid overfitting
- Consider if you have enough training data

---

## 📞 Getting Help

1. **Built-in Help**: Use ChatGPT buttons throughout the interface
2. **Sample Data**: Practice with provided examples
3. **Documentation**: Check the `docs/` folder
4. **Error Messages**: Read carefully and follow suggestions

---

**Happy analyzing with SpectraSpeak! 🌈**
