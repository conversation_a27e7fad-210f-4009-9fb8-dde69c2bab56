"""
Step 6: Model Prediction for SpectraSpeak

This step handles model prediction and validation using test data.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict, Any, Optional
from steps.base_step import BaseStep
from utils.chatgpt_helper import ChatGPTHelper
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')


class Step6ModelPrediction(BaseStep):
    """Step 6: Model Prediction - Apply trained model to test data and validate performance."""

    def __init__(self):
        super().__init__(step_number=6, step_name="Model Prediction")

    def render(self) -> None:
        """Render the model prediction interface."""
        try:
            # Header with help icon
            col1, col2 = st.columns([4, 1])
            with col1:
                st.markdown("## 🎯 Step 6: Model Prediction & Validation")
                st.markdown("*Apply your trained model to test data and validate performance*")
            with col2:
                help_icon_html = ChatGPTHelper.create_inline_help_icon(
                    "Model Prediction & Validation",
                    "understanding model prediction and validation in chemometrics",
                    """Please explain model prediction and validation in chemometrics:

1. How to properly apply a trained model to test data?
2. What metrics should be used to evaluate prediction performance?
3. How to interpret prediction vs actual plots and residuals?
4. What are the best practices for model validation?
5. How to identify potential overfitting or model issues?

Please provide guidance for effective model validation."""
                )
                st.markdown(help_icon_html, unsafe_allow_html=True)

            # Check prerequisites
            if not self._check_prerequisites():
                return

            # Get trained model from Step 5
            trained_model = self._get_trained_model()
            if not trained_model:
                st.error("❌ No trained model found. Please complete Step 5 first.")
                return

            # Main prediction interface
            self._render_prediction_interface(trained_model)

        except Exception as e:
            st.error(f"❌ Error in Step 6 render: {str(e)}")
            st.write("**Error Details:**")
            import traceback
            st.code(traceback.format_exc())

    def _check_prerequisites(self) -> bool:
        """Check if all prerequisites are met."""
        missing_items = []

        # Check if we have a trained model
        has_model = (self.session.has("final_model_config") or
                    self.session.has("selected_final_nomination") or
                    self.session.has("model_nominations"))

        if not has_model:
            missing_items.append("Trained model from Step 5")
        if not self.session.has("x_train_selected"):
            missing_items.append("Training data")

        if missing_items:
            st.error("❌ **Missing Prerequisites**")
            st.write("Please complete the following steps first:")
            for item in missing_items:
                st.write(f"• {item}")

            if st.button("🔙 Back to Step 5", key="back_to_step5_pred"):
                self.session.set_current_step(5)
                st.rerun()
            return False

        return True

    def _get_trained_model(self) -> Optional[Dict[str, Any]]:
        """Get the trained model from Step 5 session data."""
        try:
            # Try to get from selected nomination first
            selected_nomination = self.session.get("selected_final_nomination")
            if selected_nomination and isinstance(selected_nomination, dict):
                return {
                    "model": selected_nomination.get("trained_model"),
                    "algorithm": selected_nomination.get("algorithm", "Unknown"),
                    "parameters": selected_nomination.get("model_params", {}),
                    "rmsecv": selected_nomination.get("overall_rmsecv", 0),
                    "r2": selected_nomination.get("overall_r2", 0),
                    "compound_results": selected_nomination.get("compound_results", {})
                }

            # Try to get from final model config
            final_config = self.session.get("final_model_config")
            if final_config and isinstance(final_config, dict):
                results = final_config.get("results", {})
                if isinstance(results, dict):
                    return {
                        "model": self.session.get("final_model"),
                        "algorithm": final_config.get("algorithm", "Unknown"),
                        "parameters": final_config.get("model_params", {}),
                        "rmsecv": results.get("rmsecv", results.get("best_rmsecv", 0)),
                        "r2": results.get("r2", results.get("train_r2", 0)),
                        "compound_results": results.get("component_results", {})
                    }

        except Exception as e:
            st.error(f"❌ Error retrieving trained model: {str(e)}")

        return None

    def _render_prediction_interface(self, trained_model: Dict[str, Any]) -> None:
        """Render the main prediction interface."""
        # Model summary
        st.markdown("### 🤖 Model Summary")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Algorithm", trained_model["algorithm"])
        with col2:
            st.metric("Cross-Validation RMSE", f"{trained_model['rmsecv']:.4f}")
        with col3:
            st.metric("Training R²", f"{trained_model['r2']:.4f}")
        with col4:
            x_train = self.session.get("x_train_selected")
            training_samples = len(x_train) if x_train is not None else "Unknown"
            st.metric("Training Samples", training_samples)

        st.markdown("---")

        # Test data prediction
        self._render_test_prediction(trained_model)

        # Navigation
        self._render_navigation()

    def _render_test_prediction(self, trained_model: Dict[str, Any]) -> None:
        """Render test data prediction section."""
        st.markdown("### 🧪 Test Data Prediction")

        # Check if test data is available
        x_test = self.session.get("x_test")
        y_test = self.session.get("y_test")

        if x_test is None or y_test is None:
            st.warning("⚠️ Test data not available. Please upload test data in Step 1.")
            return

        # Apply same preprocessing and variable selection as training data
        x_test_processed = self._process_test_data(x_test)
        if x_test_processed is None:
            st.error("❌ Failed to process test data")
            return

        # Make predictions
        model = trained_model["model"]
        if model is None:
            st.error("❌ Trained model not available")
            return

        try:
            y_pred = model.predict(x_test_processed)
            
            # Calculate performance metrics
            y_test_numeric = y_test.select_dtypes(include=[np.number])
            
            # Display results
            self._display_prediction_results(y_test_numeric, y_pred, trained_model)
            
            # Store results for Step 7
            self.session.set("test_predictions", {
                "y_test": y_test_numeric,
                "y_pred": y_pred,
                "x_test_processed": x_test_processed
            })
            
        except Exception as e:
            st.error(f"❌ Error making predictions: {str(e)}")

    def _process_test_data(self, x_test: pd.DataFrame) -> Optional[np.ndarray]:
        """Apply same preprocessing and variable selection as training data."""
        try:
            # Apply preprocessing
            preprocessing_method = self.session.get("selected_preprocessing")
            if preprocessing_method and preprocessing_method != "None":
                # Apply same preprocessing as training data
                # This is a simplified version - in practice, you'd use the exact same preprocessing pipeline
                x_test_processed = x_test.select_dtypes(include=[np.number])
            else:
                x_test_processed = x_test.select_dtypes(include=[np.number])

            # Apply variable selection
            selected_variables = self.session.get("selected_variables")
            if selected_variables is not None:
                x_test_processed = x_test_processed.iloc[:, selected_variables]

            return x_test_processed.values

        except Exception as e:
            st.error(f"❌ Error processing test data: {str(e)}")
            return None

    def _display_prediction_results(self, y_test: pd.DataFrame, y_pred: np.ndarray, trained_model: Dict[str, Any]) -> None:
        """Display prediction results and performance metrics."""
        
        # Performance metrics
        st.markdown("#### 📊 Prediction Performance")
        
        # Calculate metrics for each compound
        compound_metrics = []
        for i, compound in enumerate(y_test.columns):
            if i < y_pred.shape[1]:
                y_true_comp = y_test.iloc[:, i].values
                y_pred_comp = y_pred[:, i] if y_pred.ndim > 1 else y_pred
                
                rmse = np.sqrt(mean_squared_error(y_true_comp, y_pred_comp))
                r2 = r2_score(y_true_comp, y_pred_comp)
                
                compound_metrics.append({
                    'Compound': compound,
                    'RMSEP': f"{rmse:.4f}",
                    'R²': f"{r2:.4f}",
                    'Performance': self._assess_performance(r2)
                })

        # Display metrics table
        if compound_metrics:
            df_metrics = pd.DataFrame(compound_metrics)
            st.dataframe(df_metrics, use_container_width=True)

        # Prediction plots
        st.markdown("#### 📈 Prediction vs Actual Plots")
        
        for i, compound in enumerate(y_test.columns):
            if i < y_pred.shape[1]:
                y_true_comp = y_test.iloc[:, i].values
                y_pred_comp = y_pred[:, i] if y_pred.ndim > 1 else y_pred
                
                fig = go.Figure()
                
                # Scatter plot
                fig.add_trace(go.Scatter(
                    x=y_true_comp,
                    y=y_pred_comp,
                    mode='markers',
                    name='Predictions',
                    marker=dict(size=8, color='blue', opacity=0.7)
                ))
                
                # Perfect prediction line
                min_val = min(y_true_comp.min(), y_pred_comp.min())
                max_val = max(y_true_comp.max(), y_pred_comp.max())
                fig.add_trace(go.Scatter(
                    x=[min_val, max_val],
                    y=[min_val, max_val],
                    mode='lines',
                    name='Perfect Prediction',
                    line=dict(color='red', dash='dash')
                ))
                
                r2 = r2_score(y_true_comp, y_pred_comp)
                fig.update_layout(
                    title=f"{compound} - Prediction vs Actual (R² = {r2:.4f})",
                    xaxis_title="Actual Values",
                    yaxis_title="Predicted Values",
                    font=dict(family="Nunito Sans", size=12),
                    height=400
                )
                
                st.plotly_chart(fig, use_container_width=True)

    def _assess_performance(self, r2: float) -> str:
        """Assess performance based on R² value."""
        if r2 > 0.9:
            return "🟢 Excellent"
        elif r2 > 0.8:
            return "🟡 Good"
        elif r2 > 0.7:
            return "🟠 Moderate"
        else:
            return "🔴 Poor"

    def _render_navigation(self) -> None:
        """Render navigation buttons."""
        st.markdown("---")
        
        # Check if predictions are complete
        predictions_complete = self.session.has("test_predictions")
        
        clicked = self.render_navigation_buttons(
            show_previous=True,
            next_enabled=predictions_complete,
            custom_next_text="Next: Model Report →" if predictions_complete else "Complete Predictions First",
            custom_previous_text="← Previous: Model Selection"
        )
        
        if not predictions_complete and clicked.get("next", False):
            st.warning("⚠️ Please complete model predictions before proceeding.")
            return
            
        self.handle_navigation(clicked)

    def validate_step_completion(self) -> bool:
        """Validate that the step is complete."""
        return self.session.has("test_predictions")
