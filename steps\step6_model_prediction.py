"""
Step 6: Model Prediction for SpectraSpeak

This step handles model prediction and validation using test data.
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict, Any, Optional
from steps.base_step import BaseStep
from utils.chatgpt_helper import ChatGPTHelper
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')


class Step6ModelPrediction(BaseStep):
    """Step 6: Model Prediction - Apply trained model to test data and validate performance."""

    def __init__(self):
        super().__init__(step_number=6, step_name="Model Prediction")

    def render(self) -> None:
        """Render the model prediction interface."""
        try:
            # Header with help icon
            col1, col2 = st.columns([4, 1])
            with col1:
                st.markdown("## 🎯 Step 6: Model Prediction & Validation")
                st.markdown("*Apply your trained model to test data and validate performance*")
            with col2:
                help_icon_html = ChatGPTHelper.create_inline_help_icon(
                    "Model Prediction & Validation",
                    "understanding model prediction and validation in chemometrics",
                    """Please explain model prediction and validation in chemometrics:

1. How to properly apply a trained model to test data?
2. What metrics should be used to evaluate prediction performance?
3. How to interpret prediction vs actual plots and residuals?
4. What are the best practices for model validation?
5. How to identify potential overfitting or model issues?

Please provide guidance for effective model validation."""
                )
                st.markdown(help_icon_html, unsafe_allow_html=True)

            # Check prerequisites
            if not self._check_prerequisites():
                return

            # Get trained model from Step 5
            trained_model = self._get_trained_model()
            if not trained_model:
                st.error("❌ No trained model found. Please complete Step 5 first.")
                return

            # Main prediction interface
            self._render_prediction_interface(trained_model)

        except Exception as e:
            st.error(f"❌ Error in Step 6 render: {str(e)}")
            st.write("**Error Details:**")
            import traceback
            st.code(traceback.format_exc())

    def _check_prerequisites(self) -> bool:
        """Check if all prerequisites are met."""
        missing_items = []

        # Check if we have a trained model
        has_model = (self.session.has("final_model_config") or
                    self.session.has("selected_final_nomination") or
                    self.session.has("model_nominations"))

        if not has_model:
            missing_items.append("Trained model from Step 5")
        if not self.session.has("x_train_selected"):
            missing_items.append("Training data")

        if missing_items:
            st.error("❌ **Missing Prerequisites**")
            st.write("Please complete the following steps first:")
            for item in missing_items:
                st.write(f"• {item}")

            if st.button("🔙 Back to Step 5", key="back_to_step5_pred"):
                self.session.set_current_step(5)
                st.rerun()
            return False

        return True

    def _get_trained_model(self) -> Optional[Dict[str, Any]]:
        """Get the trained model from Step 5 session data."""
        try:
            # Try to get from selected nomination first
            selected_nomination = self.session.get("selected_final_nomination")
            if selected_nomination and isinstance(selected_nomination, dict):
                return {
                    "model": selected_nomination.get("trained_model"),
                    "algorithm": selected_nomination.get("algorithm", "Unknown"),
                    "parameters": selected_nomination.get("model_params", {}),
                    "rmsecv": selected_nomination.get("overall_rmsecv", 0),
                    "r2": selected_nomination.get("overall_r2", 0),
                    "compound_results": selected_nomination.get("compound_results", {})
                }

            # Try to get from final model config
            final_config = self.session.get("final_model_config")
            if final_config and isinstance(final_config, dict):
                results = final_config.get("results", {})
                if isinstance(results, dict):
                    return {
                        "model": self.session.get("final_model"),
                        "algorithm": final_config.get("algorithm", "Unknown"),
                        "parameters": final_config.get("model_params", {}),
                        "rmsecv": results.get("rmsecv", results.get("best_rmsecv", 0)),
                        "r2": results.get("r2", results.get("train_r2", 0)),
                        "compound_results": results.get("component_results", {})
                    }

        except Exception as e:
            st.error(f"❌ Error retrieving trained model: {str(e)}")

        return None

    def _render_prediction_interface(self, trained_model: Dict[str, Any]) -> None:
        """Render the main prediction interface."""
        # Model summary
        st.markdown("### 🤖 Model Summary")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Algorithm", trained_model["algorithm"])
        with col2:
            st.metric("Cross-Validation RMSE", f"{trained_model['rmsecv']:.4f}")
        with col3:
            st.metric("Training R²", f"{trained_model['r2']:.4f}")
        with col4:
            x_train = self.session.get("x_train_selected")
            training_samples = len(x_train) if x_train is not None else "Unknown"
            st.metric("Training Samples", training_samples)

        st.markdown("---")

        # Test data upload/selection
        self._render_test_data_section()

        st.markdown("---")

        # Test data prediction
        self._render_test_prediction(trained_model)

        # Navigation
        self._render_navigation()

    def _render_test_data_section(self) -> None:
        """Render test data upload/selection section."""
        st.markdown("### 📁 Test Data")

        # Check if test data is already available from Step 1
        x_test_step1 = self.session.get("x_test")
        y_test_step1 = self.session.get("y_test")

        # Check if test data was uploaded in this step
        x_test_step6 = self.session.get("x_test_step6")
        y_test_step6 = self.session.get("y_test_step6")

        # Create tabs for different test data options
        tab1, tab2, tab3 = st.tabs(["📊 Current Test Data", "📤 Upload New Test Data", "ℹ️ Data Requirements"])

        with tab1:
            st.markdown("#### Current Test Data Status")

            if x_test_step6 is not None and y_test_step6 is not None:
                st.success("✅ **Test data uploaded in Step 6**")
                x_test_numeric = x_test_step6.select_dtypes(include=[np.number])
                y_test_numeric = y_test_step6.select_dtypes(include=[np.number])

                col1, col2 = st.columns(2)
                with col1:
                    st.write(f"**X_test samples:** {len(x_test_step6)}")
                    st.write(f"**X_test variables:** {len(x_test_numeric.columns)}")
                with col2:
                    st.write(f"**Y_test samples:** {len(y_test_step6)}")
                    st.write(f"**Y_test compounds:** {len(y_test_numeric.columns)}")
                    st.write(f"**Compounds:** {', '.join(y_test_numeric.columns)}")

                if st.button("🗑️ Clear Step 6 Test Data", key="clear_step6_data"):
                    self.session.delete("x_test_step6")
                    self.session.delete("y_test_step6")
                    st.success("✅ Step 6 test data cleared")
                    st.rerun()

            elif x_test_step1 is not None and y_test_step1 is not None:
                st.info("ℹ️ **Using test data from Step 1**")
                x_test_numeric = x_test_step1.select_dtypes(include=[np.number])
                y_test_numeric = y_test_step1.select_dtypes(include=[np.number])

                col1, col2 = st.columns(2)
                with col1:
                    st.write(f"**X_test samples:** {len(x_test_step1)}")
                    st.write(f"**X_test variables:** {len(x_test_numeric.columns)}")
                with col2:
                    st.write(f"**Y_test samples:** {len(y_test_step1)}")
                    st.write(f"**Y_test compounds:** {len(y_test_numeric.columns)}")
                    st.write(f"**Compounds:** {', '.join(y_test_numeric.columns)}")
            else:
                st.warning("⚠️ **No test data available**")
                st.write("Please upload test data using the 'Upload New Test Data' tab or go back to Step 1.")

        with tab2:
            st.markdown("#### Upload New Test Data")
            st.info("💡 Upload test data here to validate your model or test with different datasets.")

            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**X_test (Spectral Data)**")
                x_test_file = st.file_uploader(
                    "Upload X_test file",
                    type=['csv', 'xlsx'],
                    key="x_test_step6_upload",
                    help="Spectral data for testing (samples × variables)"
                )

                if x_test_file is not None:
                    try:
                        if x_test_file.name.endswith('.csv'):
                            x_test_new = pd.read_csv(x_test_file)
                        else:
                            x_test_new = pd.read_excel(x_test_file)

                        st.success(f"✅ X_test loaded: {x_test_new.shape[0]} samples × {x_test_new.shape[1]} variables")

                        # Preview
                        with st.expander("📋 Preview X_test"):
                            st.dataframe(x_test_new.head(), use_container_width=True)

                        # Store temporarily
                        self.session.set("x_test_step6_temp", x_test_new)

                    except Exception as e:
                        st.error(f"❌ Error loading X_test: {str(e)}")

            with col2:
                st.markdown("**Y_test (Reference Values)**")
                y_test_file = st.file_uploader(
                    "Upload Y_test file",
                    type=['csv', 'xlsx'],
                    key="y_test_step6_upload",
                    help="Reference values for testing (samples × compounds)"
                )

                if y_test_file is not None:
                    try:
                        if y_test_file.name.endswith('.csv'):
                            y_test_new = pd.read_csv(y_test_file)
                        else:
                            y_test_new = pd.read_excel(y_test_file)

                        st.success(f"✅ Y_test loaded: {y_test_new.shape[0]} samples × {y_test_new.shape[1]} compounds")

                        # Preview
                        with st.expander("📋 Preview Y_test"):
                            st.dataframe(y_test_new.head(), use_container_width=True)

                        # Store temporarily
                        self.session.set("y_test_step6_temp", y_test_new)

                    except Exception as e:
                        st.error(f"❌ Error loading Y_test: {str(e)}")

            # Confirm upload button
            x_test_temp = self.session.get("x_test_step6_temp")
            y_test_temp = self.session.get("y_test_step6_temp")

            if x_test_temp is not None and y_test_temp is not None:
                st.markdown("---")
                col1, col2, col3 = st.columns([1, 2, 1])
                with col2:
                    if st.button("✅ Confirm Test Data Upload", type="primary", key="confirm_test_data"):
                        # Validate data compatibility
                        validation_result = self._validate_test_data(x_test_temp, y_test_temp)

                        if validation_result["valid"]:
                            # Store the test data
                            self.session.set("x_test_step6", x_test_temp)
                            self.session.set("y_test_step6", y_test_temp)

                            # Clean up temporary data
                            self.session.delete("x_test_step6_temp")
                            self.session.delete("y_test_step6_temp")

                            st.success("🎉 Test data uploaded successfully!")
                            st.rerun()
                        else:
                            st.error("❌ Test data validation failed:")
                            for error in validation_result["errors"]:
                                st.write(f"• {error}")

        with tab3:
            st.markdown("#### Data Requirements")
            st.markdown("""
            **For successful model prediction, your test data must:**

            📊 **X_test (Spectral Data):**
            - Same number of variables as training data
            - Variables in the same order as training data
            - Same wavelength range and resolution
            - No missing values in spectroscopic data
            - CSV or Excel format

            🎯 **Y_test (Reference Values):**
            - Same compounds as training data
            - Compounds in the same order
            - Known reference values for validation
            - CSV or Excel format

            ⚠️ **Important Notes:**
            - Test data will undergo the same preprocessing as training data
            - Same variable selection will be applied automatically
            - Ensure data quality and consistency with training data
            - Use independent test samples for unbiased validation
            """)

    def _validate_test_data(self, x_test: pd.DataFrame, y_test: pd.DataFrame) -> Dict[str, Any]:
        """Validate test data compatibility with training data."""
        errors = []

        try:
            # Get training data info
            x_train = self.session.get("x_train_selected")
            y_train = self.session.get("y_train")

            if x_train is None or y_train is None:
                errors.append("Training data not available for validation")
                return {"valid": False, "errors": errors}

            # Check X data compatibility
            x_test_numeric = x_test.select_dtypes(include=[np.number])
            x_train_numeric = x_train.select_dtypes(include=[np.number])

            if len(x_test_numeric.columns) != len(x_train_numeric.columns):
                errors.append(f"X_test has {len(x_test_numeric.columns)} variables, but training data has {len(x_train_numeric.columns)}")

            # Check Y data compatibility
            y_test_numeric = y_test.select_dtypes(include=[np.number])
            y_train_numeric = y_train.select_dtypes(include=[np.number])

            if len(y_test_numeric.columns) != len(y_train_numeric.columns):
                errors.append(f"Y_test has {len(y_test_numeric.columns)} compounds, but training data has {len(y_train_numeric.columns)}")

            # Check compound names
            if not all(col in y_train_numeric.columns for col in y_test_numeric.columns):
                errors.append("Y_test compound names don't match training data compounds")

            # Check for missing values
            if x_test_numeric.isnull().sum().sum() > 0:
                errors.append("X_test contains missing values")

            if y_test_numeric.isnull().sum().sum() > 0:
                errors.append("Y_test contains missing values")

            # Check sample count consistency
            if len(x_test) != len(y_test):
                errors.append(f"X_test has {len(x_test)} samples but Y_test has {len(y_test)} samples")

        except Exception as e:
            errors.append(f"Validation error: {str(e)}")

        return {"valid": len(errors) == 0, "errors": errors}

    def _render_test_prediction(self, trained_model: Dict[str, Any]) -> None:
        """Render test data prediction section."""
        st.markdown("### 🧪 Test Data Prediction")

        # Get test data (prioritize Step 6 data over Step 1 data)
        x_test_step6 = self.session.get("x_test_step6")
        y_test_step6 = self.session.get("y_test_step6")
        x_test_step1 = self.session.get("x_test")
        y_test_step1 = self.session.get("y_test")

        # Determine which test data to use
        if x_test_step6 is not None and y_test_step6 is not None:
            x_test = x_test_step6
            y_test = y_test_step6
            data_source = "Step 6"
            st.info("ℹ️ Using test data uploaded in Step 6")
        elif x_test_step1 is not None and y_test_step1 is not None:
            x_test = x_test_step1
            y_test = y_test_step1
            data_source = "Step 1"
            st.info("ℹ️ Using test data from Step 1")
        else:
            st.warning("⚠️ No test data available. Please upload test data using the 'Upload New Test Data' tab above.")
            return

        # Apply same preprocessing and variable selection as training data
        x_test_processed = self._process_test_data(x_test)
        if x_test_processed is None:
            st.error("❌ Failed to process test data")
            return

        # Make predictions
        model = trained_model["model"]
        if model is None:
            st.error("❌ Trained model not available")
            return

        try:
            y_pred = model.predict(x_test_processed)

            # Calculate performance metrics
            y_test_numeric = y_test.select_dtypes(include=[np.number])

            # Display results
            self._display_prediction_results(y_test_numeric, y_pred, trained_model)

            # Store results for Step 7
            self.session.set("test_predictions", {
                "y_test": y_test_numeric,
                "y_pred": y_pred,
                "x_test_processed": x_test_processed
            })

        except Exception as e:
            st.error(f"❌ Error making predictions: {str(e)}")

    def _process_test_data(self, x_test: pd.DataFrame) -> Optional[np.ndarray]:
        """Apply same preprocessing and variable selection as training data."""
        try:
            # Apply preprocessing
            preprocessing_method = self.session.get("selected_preprocessing")
            if preprocessing_method and preprocessing_method != "None":
                # Apply same preprocessing as training data
                # This is a simplified version - in practice, you'd use the exact same preprocessing pipeline
                x_test_processed = x_test.select_dtypes(include=[np.number])
            else:
                x_test_processed = x_test.select_dtypes(include=[np.number])

            # Apply variable selection
            selected_variables = self.session.get("selected_variables")
            if selected_variables is not None:
                x_test_processed = x_test_processed.iloc[:, selected_variables]

            return x_test_processed.values

        except Exception as e:
            st.error(f"❌ Error processing test data: {str(e)}")
            return None

    def _display_prediction_results(self, y_test: pd.DataFrame, y_pred: np.ndarray, trained_model: Dict[str, Any]) -> None:
        """Display prediction results and performance metrics."""

        # Performance metrics
        st.markdown("#### 📊 Prediction Performance")

        # Calculate metrics for each compound
        compound_metrics = []
        for i, compound in enumerate(y_test.columns):
            if i < y_pred.shape[1]:
                y_true_comp = y_test.iloc[:, i].values
                y_pred_comp = y_pred[:, i] if y_pred.ndim > 1 else y_pred

                rmse = np.sqrt(mean_squared_error(y_true_comp, y_pred_comp))
                r2 = r2_score(y_true_comp, y_pred_comp)

                compound_metrics.append({
                    'Compound': compound,
                    'RMSEP': f"{rmse:.4f}",
                    'R²': f"{r2:.4f}",
                    'Performance': self._assess_performance(r2)
                })

        # Display metrics table
        if compound_metrics:
            df_metrics = pd.DataFrame(compound_metrics)
            st.dataframe(df_metrics, use_container_width=True)

        # Prediction plots
        st.markdown("#### 📈 Prediction vs Actual Plots")

        for i, compound in enumerate(y_test.columns):
            if i < y_pred.shape[1]:
                y_true_comp = y_test.iloc[:, i].values
                y_pred_comp = y_pred[:, i] if y_pred.ndim > 1 else y_pred

                fig = go.Figure()

                # Scatter plot
                fig.add_trace(go.Scatter(
                    x=y_true_comp,
                    y=y_pred_comp,
                    mode='markers',
                    name='Predictions',
                    marker=dict(size=8, color='blue', opacity=0.7)
                ))

                # Perfect prediction line
                min_val = min(y_true_comp.min(), y_pred_comp.min())
                max_val = max(y_true_comp.max(), y_pred_comp.max())
                fig.add_trace(go.Scatter(
                    x=[min_val, max_val],
                    y=[min_val, max_val],
                    mode='lines',
                    name='Perfect Prediction',
                    line=dict(color='red', dash='dash')
                ))

                r2 = r2_score(y_true_comp, y_pred_comp)
                fig.update_layout(
                    title=f"{compound} - Prediction vs Actual (R² = {r2:.4f})",
                    xaxis_title="Actual Values",
                    yaxis_title="Predicted Values",
                    font=dict(family="Nunito Sans", size=12),
                    height=400
                )

                st.plotly_chart(fig, use_container_width=True)

    def _assess_performance(self, r2: float) -> str:
        """Assess performance based on R² value."""
        if r2 > 0.9:
            return "🟢 Excellent"
        elif r2 > 0.8:
            return "🟡 Good"
        elif r2 > 0.7:
            return "🟠 Moderate"
        else:
            return "🔴 Poor"

    def _render_navigation(self) -> None:
        """Render navigation buttons."""
        st.markdown("---")

        # Check if test data is available and predictions are complete
        has_test_data = (self.session.has("x_test_step6") and self.session.has("y_test_step6")) or \
                       (self.session.has("x_test") and self.session.has("y_test"))
        predictions_complete = self.session.has("test_predictions")

        # Determine next button state
        if not has_test_data:
            next_enabled = False
            next_text = "Upload Test Data First"
        elif not predictions_complete:
            next_enabled = False
            next_text = "Complete Predictions First"
        else:
            next_enabled = True
            next_text = "Next: Model Report →"

        clicked = self.render_navigation_buttons(
            show_previous=True,
            next_enabled=next_enabled,
            custom_next_text=next_text,
            custom_previous_text="← Previous: Model Selection"
        )

        if not next_enabled and clicked.get("next", False):
            if not has_test_data:
                st.warning("⚠️ Please upload test data before proceeding.")
            else:
                st.warning("⚠️ Please complete model predictions before proceeding.")
            return

        self.handle_navigation(clicked)

    def validate_step_completion(self) -> bool:
        """Validate that the step is complete."""
        return self.session.has("test_predictions")
