# SpectraSpeak

An intelligent spectroscopic analysis platform for advanced chemometric data analysis using neural intelligence, vector machines, and ensemble learning.

## Features

### Data Upload and Inspection
- Upload X_train, Y_train, and X_test data (CSV files)
- Display data shape and preview
- Check for missing values
- Show statistics for Y_train (min, max, mean, std)

### Preprocessing & Model Selection
- Select from multiple preprocessing methods:
  - Mean-centering
  - Autoscaling
  - SNV (Standard Normal Variate)
  - <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON> 1st derivative
  - SNV + 1st derivative
- Automatic 10-fold cross-validation for each preprocessing method
- Interactive plot of RMSECV vs. latent variables
- Summary table with optimal LV and RMSECV for each method
- Automatic recommendation of the best preprocessing method

### Variable Selection
- Choose between:
  - VIP filtering (with adjustable threshold)
  - Genetic Algorithm (with customizable parameters)
- Visualization of selected variables
- Evaluation of model improvement after variable selection

### Final Model
- Model summary with preprocessing method, variable selection, LV, RMSE, R², etc.
- Explained variance plots
- Predicted vs. reference plots with confidence intervals
- Residuals vs. leverage (<PERSON><PERSON>'s T²) plot for outlier detection
- Textual interpretation of the model

### Predictions
- Predict unknowns (X_test) using the final model
- Display predictions with 95% prediction intervals
- Downloadable CSV with all predictions and error estimates
- Visualization of predictions for each analyte

## Installation

1. Clone this repository:
```
git clone <repository-url>
cd <repository-directory>
```

2. Install the required packages:
```
pip install -r requirements.txt
```

## Usage

1. Run SpectraSpeak:
```bash
# Windows
run_spectraspeak.bat

# Or manually
python -m streamlit run main.py --server.port 8527
```

2. Open your web browser and navigate to the URL displayed in the terminal (usually http://localhost:8501)

3. Upload your data files or use the sample data option

4. Follow the workflow through the tabs:
   - Data Overview
   - Preprocessing & Model Selection
   - Variable Selection
   - Final Model
   - Predictions

## Data Format

The application expects CSV files with the following format:

- **X_train.csv**: Predictor variables for training (samples in rows, variables in columns)
- **Y_train.csv**: Response variables for training (samples in rows, analytes in columns)
- **X_test.csv**: Predictor variables for prediction (samples in rows, variables in columns)

The CSV files should use semicolon (;) as the delimiter and comma (,) as the decimal separator.

## Dependencies

- Python 3.7+
- numpy
- pandas
- scikit-learn
- scipy
- matplotlib
- plotly
- streamlit

## License

This project is licensed under the MIT License - see the LICENSE file for details.
