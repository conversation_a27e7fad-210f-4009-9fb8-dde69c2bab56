"""
Reusable Logo Component for SpectraSpeak

This module provides a centralized logo component that can be easily updated
and configured for different branding requirements.
"""

import streamlit as st
from config.settings import BRANDING_CONFIG, UI_COLORS


class LogoComponent:
    """Reusable logo component with configurable branding."""

    @staticmethod
    def render_spectroscopy_logo(
        width: int = None,
        height: int = None,
        text: str = None,
        mobile_responsive: bool = True
    ) -> str:
        """Render the spectroscopy-themed logo."""

        # Use config defaults if not specified
        logo_config = BRANDING_CONFIG['logo']
        width = width or logo_config['width']
        height = height or logo_config['height']
        text = text or logo_config['text']

        mobile_width = logo_config['mobile_width']
        mobile_height = logo_config['mobile_height']

        responsive_css = ""
        if mobile_responsive:
            responsive_css = f"""
            @media (max-width: 768px) {{
                .spectro-logo {{
                    width: {mobile_width}px !important;
                    height: {mobile_height}px !important;
                }}
            }}

            @media (max-width: 480px) {{
                .spectro-logo {{
                    width: {mobile_width}px !important;
                    height: {mobile_height}px !important;
                }}

                .spectrum-text {{
                    font-size: 0.5rem !important;
                }}
            }}
            """

        return f"""
        <style>
        {responsive_css}
        </style>
        <div class="spectro-logo" style="width: {width}px; height: {height}px;">
            <div class="spectrum-text">{text}</div>
        </div>
        """

    @staticmethod
    def render_custom_logo(logo_path: str, alt_text: str = "SpectraSpeak") -> str:
        """Render a custom logo from file path."""
        return f"""
        <div class="custom-logo">
            <img src="{logo_path}" alt="{alt_text}" style="max-height: 60px; max-width: 80px;">
        </div>
        """

    @staticmethod
    def render_text_logo(text: str, style: str = "gradient") -> str:
        """Render a text-based logo."""
        if style == "gradient":
            return f"""
            <div class="text-logo gradient-text">
                {text}
            </div>
            """
        else:
            return f"""
            <div class="text-logo simple-text">
                {text}
            </div>
            """

    @staticmethod
    def get_logo_html() -> str:
        """Get the appropriate logo HTML based on configuration."""
        logo_config = BRANDING_CONFIG['logo']

        if logo_config['type'] == 'spectroscopy':
            return LogoComponent.render_spectroscopy_logo()
        elif logo_config['type'] == 'custom' and BRANDING_CONFIG.get('custom_logo_path'):
            return LogoComponent.render_custom_logo(BRANDING_CONFIG['custom_logo_path'])
        elif logo_config['type'] == 'text':
            return LogoComponent.render_text_logo(BRANDING_CONFIG['app_title'])
        else:
            # Default to spectroscopy logo
            return LogoComponent.render_spectroscopy_logo()


class HeaderComponent:
    """Complete header component with logo and branding."""

    @staticmethod
    def render_main_header() -> None:
        """Render the complete main application header using Streamlit components."""
        branding = BRANDING_CONFIG

        # Use Streamlit's native components for better reliability
        st.markdown("""
        <style>
        @import url('https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@300;400;500;600;700;800;900&display=swap');

        .header-container {
            text-align: center;
            padding: 1rem 0 0.8rem 0;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 50%, #bbdefb 100%);
            border-radius: 0 0 15px 15px;
            box-shadow: 0 2px 12px rgba(30, 136, 229, 0.12);
            border-bottom: 3px solid #1E88E5;
        }

        .title-row {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            margin-bottom: 0.5rem;
        }

        .logo-icon {
            width: 50px;
            height: 40px;
            background: linear-gradient(135deg, #1E88E5 0%, #42A5F5 100%);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            box-shadow: 0 2px 8px rgba(30, 136, 229, 0.3);
        }

        .main-title {
            font-size: 2.8rem;
            font-weight: 800;
            background: linear-gradient(135deg, #1E88E5 0%, #42A5F5 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            font-family: 'Nunito Sans', sans-serif;
            line-height: 1;
        }

        .version-badge {
            background: rgba(30, 136, 229, 0.1);
            padding: 0.2rem 0.6rem;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 600;
            color: #0D47A1;
            font-family: 'Nunito Sans', sans-serif;
        }

        .subtitle {
            font-size: 1.1rem;
            font-weight: 500;
            color: #2F4F4F;
            margin: 0.3rem 0 0.6rem 0;
            font-family: 'Nunito Sans', sans-serif;
        }

        .model-tags {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin: 0.5rem 0;
            flex-wrap: wrap;
            max-width: 900px;
            margin-left: auto;
            margin-right: auto;
        }

        .model-tag {
            color: white;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 500;
            font-family: 'Nunito Sans', sans-serif;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .tag-1 { background: linear-gradient(135deg, #1E88E5 0%, #0D47A1 100%); }
        .tag-2 { background: linear-gradient(135deg, #42A5F5 0%, #1E88E5 100%); }
        .tag-3 { background: linear-gradient(135deg, #0D47A1 0%, #42A5F5 100%); }

        @media (max-width: 768px) {
            .main-title { font-size: 2.2rem !important; }
            .model-tags { gap: 0.3rem !important; }
            .model-tag { font-size: 0.6rem !important; padding: 0.15rem 0.4rem !important; }
        }
        </style>
        """, unsafe_allow_html=True)

        # Now render the header using CSS classes
        st.markdown(f"""
        <div class="header-container">
            <div class="title-row">
                <div class="logo-icon">🔬</div>
                <h1 class="main-title">{branding['app_title']}</h1>
                <div class="version-badge">v{branding['version']}</div>
            </div>
            <p class="subtitle">{branding['app_subtitle']}</p>
            <div class="model-tags">
                <span class="model-tag tag-1">🧠 Neural Intelligence</span>
                <span class="model-tag tag-2">🎯 Vector Machines</span>
                <span class="model-tag tag-3">🔮 Ensemble Learning</span>
                <span class="model-tag tag-1">💎 Spectral Analytics</span>
                <span class="model-tag tag-2">⚡ Smart Regression</span>
                <span class="model-tag tag-3">🌟 Variable Selection</span>
                <span class="model-tag tag-1">🚀 Gradient Boosting</span>
                <span class="model-tag tag-2">🔬 Data Preprocessing</span>
                <span class="model-tag tag-3">📊 Cross-Validation</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def render_compact_header() -> None:
        """Render a compact version of the header for smaller spaces."""
        branding = BRANDING_CONFIG
        logo_html = LogoComponent.get_logo_html()

        compact_header_html = f"""
        <div class="compact-header">
            <div class="compact-title-container">
                {logo_html}
                <h2 class="compact-title">{branding['app_title']}</h2>
            </div>
        </div>
        """

        st.markdown(compact_header_html, unsafe_allow_html=True)


class BrandingManager:
    """Manages branding consistency across the application."""

    @staticmethod
    def get_page_config() -> dict:
        """Get Streamlit page configuration with branding."""
        branding = BRANDING_CONFIG

        return {
            'page_title': branding['app_title'],
            'page_icon': branding['page_icon'],
            'layout': "wide",
            'initial_sidebar_state': "expanded",
            'menu_items': {
                'About': f"# {branding['app_title']}\n{branding['app_subtitle']}\n\nVersion {branding['version']}"
            }
        }

    @staticmethod
    def apply_branding_to_page() -> None:
        """Apply consistent branding to the current page."""
        # Set page config
        config = BrandingManager.get_page_config()
        st.set_page_config(**config)

        # Add custom favicon if specified
        if BRANDING_CONFIG.get('favicon_path'):
            st.markdown(
                f'<link rel="icon" type="image/x-icon" href="{BRANDING_CONFIG["favicon_path"]}">',
                unsafe_allow_html=True
            )

    @staticmethod
    def get_brand_colors() -> dict:
        """Get brand colors for consistent theming."""
        return UI_COLORS

    @staticmethod
    def update_branding(new_config: dict) -> None:
        """Update branding configuration (for future extensibility)."""
        # This would update the configuration
        # For now, it's a placeholder for future dynamic branding updates
        pass


# Convenience functions for easy import
def render_logo() -> str:
    """Convenience function to render the logo."""
    return LogoComponent.get_logo_html()


def render_header() -> None:
    """Convenience function to render the main header."""
    HeaderComponent.render_main_header()


def get_page_config() -> dict:
    """Convenience function to get page configuration."""
    return BrandingManager.get_page_config()


def apply_branding() -> None:
    """Convenience function to apply branding to page."""
    BrandingManager.apply_branding_to_page()
